###
  description: Process property translation queue with priority-based service selection
  Options:
    force: ignore running watch
  Usage:
    nohup ./start.sh lib/batchBase.coffee batch/prop/propTranslationQueueProcessor.coffee > ./logs/propTranslationQueue.log 2>&1 &
  New Config:
    ./start.sh -t batch -n propTranslationQueueProcessor -cmd 'lib/batchBase.coffee batch/prop/propTranslationQueueProcessor.coffee'
###

PropTranslationQueue = INCLUDE 'libapp.propTranslationQueue'
translatorManagerLib = INCLUDE 'lib.translator/translatorManager'
libStaticListing = INCLUDE 'model.staticRMlisting'
speed = INCLUDE 'lib.speed'

conf = CONFIG(['azure','deepseek','deepL','openAI','gemini','claude','grok','rm','endpoints'])
debug = DEBUG()
avgs = AVGS
PropertiesModel = MODEL 'Properties'
PropertiesCol = COLLECTION('vow', 'properties')
SysdataCol = COLLECTION('vow','sysdata')
ProcessStatusCol = COLLECTION('vow','processStatus')

# 初始化翻译管理器
translatorManager = translatorManagerLib.createTranslatorManager(conf)
propTranslationQueue = new PropTranslationQueue()

# 配置参数
BATCH_SIZE = 20
PROCESS_INTERVAL_MS = 5000  # 5秒处理间隔
MAX_RETRIES = 3
PROCESS_STATUS_ID = 'propTranslationQueueProcessor'

# 性能监控
speedMeter = speed.createSpeedMeter()

# 全局变量
gProcessing = false
gShouldStop = false

# 处理单个翻译任务
processTranslationTask = (task) ->
  try
    debug.info 'Processing translation task', task._id, task.priority
    
    # 获取房源信息
    prop = await PropertiesCol.findOne({_id: task._id})
    if not prop
      debug.warn 'Property not found for translation task', task._id
      await propTranslationQueue.markTaskFailed(task._id, new Error('Property not found'))
      return
    
    # 检查是否还需要翻译
    if prop.m_zh
      debug.info 'Property already has translation, skipping', task._id
      await propTranslationQueue.markTaskCompleted(task._id, prop.m_zh, 'already_translated')
      return
    
    if not prop.m
      debug.warn 'Property has no content to translate', task._id
      await propTranslationQueue.markTaskFailed(task._id, new Error('No content to translate'))
      return
    
    # 预处理文本内容
    toBeTranslatedStr = replaceM(prop.m)
    if not toBeTranslatedStr
      debug.info 'No content after preprocessing', task._id
      await propTranslationQueue.markTaskCompleted(task._id, '', 'no_content')
      return
    
    # 获取翻译服务列表
    translationServices = task.models or propTranslationQueue.getTranslationServiceList(task.priority)
    
    # 执行翻译
    [translation, usedService] = await translatorManager.translate(toBeTranslatedStr, translationServices)
    
    if not translation or typeof translation isnt 'string'
      throw new Error('Invalid translation response')
    
    # 更新房源的翻译字段
    await PropertiesModel.updateMZh({
      m_zh: translation,
      propId: task._id
    })
    
    # 标记任务完成
    await propTranslationQueue.markTaskCompleted(task._id, translation, usedService)
    
    speedMeter.check {"translateSuccess_#{usedService}": 1}
    debug.info 'Translation task completed', task._id, usedService
    
  catch error
    debug.error 'Translation task failed', error, task._id
    await propTranslationQueue.markTaskFailed(task._id, error, MAX_RETRIES)
    speedMeter.check {"translateFailed": 1}

# 文本预处理函数
replaceM = (m='') ->
  if not m
    return m
  m = m+''
  for i in libStaticListing.propMReplaceList
    m = m.replace i.reg, i.to
  return m

# 批量处理翻译任务
processBatch = ->
  if gProcessing
    debug.debug 'Already processing, skipping this cycle'
    return
  
  gProcessing = true
  
  try
    # 获取下一批任务
    tasks = await propTranslationQueue.getNextTranslationBatch(BATCH_SIZE)
    
    if tasks.length is 0
      debug.debug 'No pending translation tasks'
      speedMeter.check {noPendingTasks: 1}
      return
    
    debug.info "Processing #{tasks.length} translation tasks"
    speedMeter.check {batchSize: tasks.length}
    
    # 并发处理任务（限制并发数）
    concurrencyLimit = Math.min(5, tasks.length)
    taskPromises = []
    
    for i in [0...concurrencyLimit]
      if i < tasks.length
        taskPromises.push processTranslationTask(tasks[i])
    
    # 等待第一批任务完成
    await Promise.allSettled(taskPromises)
    
    # 处理剩余任务
    if tasks.length > concurrencyLimit
      remainingTasks = tasks.slice(concurrencyLimit)
      for task in remainingTasks
        if gShouldStop
          break
        await processTranslationTask(task)
    
    speedMeter.check {batchProcessed: 1}
    
  catch error
    debug.error 'Error in processBatch', error
    speedMeter.check {batchError: 1}
  finally
    gProcessing = false

# 更新处理状态
updateProcessStatus = ->
  try
    stats = await propTranslationQueue.getQueueStats()
    if stats
      await ProcessStatusCol.updateOne(
        {_id: PROCESS_STATUS_ID},
        {
          $set: {
            lastUpdate: new Date()
            queueStats: stats
            processing: gProcessing
          }
        },
        {upsert: true}
      )
  catch error
    debug.error 'Error updating process status', error

# 清理旧任务
cleanupOldTasks = ->
  try
    deletedCount = await propTranslationQueue.cleanupCompletedTasks(7) # 清理7天前的已完成任务
    if deletedCount > 0
      debug.info "Cleaned up #{deletedCount} old completed tasks"
      speedMeter.check {cleanedTasks: deletedCount}
  catch error
    debug.error 'Error cleaning up old tasks', error

# 主处理循环
mainLoop = ->
  debug.info 'Starting property translation queue processor'
  
  # 定期处理任务
  processingInterval = setInterval ->
    if gShouldStop
      clearInterval processingInterval
      return
    processBatch()
  , PROCESS_INTERVAL_MS
  
  # 定期更新状态
  statusInterval = setInterval ->
    if gShouldStop
      clearInterval statusInterval
      return
    updateProcessStatus()
  , 30000  # 30秒更新一次状态
  
  # 定期清理旧任务
  cleanupInterval = setInterval ->
    if gShouldStop
      clearInterval cleanupInterval
      return
    cleanupOldTasks()
  , 3600000  # 1小时清理一次

# 优雅退出处理
gracefulExit = (signal) ->
  debug.info "Received #{signal}, shutting down gracefully..."
  gShouldStop = true
  
  # 等待当前处理完成
  waitForCompletion = ->
    if gProcessing
      debug.info 'Waiting for current batch to complete...'
      setTimeout waitForCompletion, 1000
    else
      debug.info 'Property translation queue processor stopped'
      process.exit(0)
  
  waitForCompletion()

# 注册信号处理
process.on 'SIGINT', -> gracefulExit('SIGINT')
process.on 'SIGTERM', -> gracefulExit('SIGTERM')

# 启动处理器
if require.main is module
  mainLoop()

module.exports = {
  processBatch,
  processTranslationTask,
  updateProcessStatus,
  cleanupOldTasks
}
