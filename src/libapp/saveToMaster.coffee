geolib = require 'geolib'
# TODO: use helpers
{relog,unifyAddress} = require '../lib/helpers_string'
{inputToDateNum,inputToDate,dayDiff} = require '../lib/helpers_date'
functionHelper = require '../lib/helpers_function'
{ cleanInvalidFields } = require '../lib/helpers_object'
cityHelper = require '../lib/cityHelper'
# GeoCoder = MODEL 'GeoCoder'
PropertiesHelper = require './properties'
impMappingTreb = require './impMappingTreb'
impMappingDdf = require './impMappingDdf'
impMappingBcreManual = require './impMappingBcreManual'
impMappingBcre = require './impMappingBcreAuto'
impMappingBcreReso = require './impMappingBcreReso'
impMappingProperty = require './impMappingProperty'
impMappingRahb = require './impMappingRahb'
impMappingCreb = require './impMappingCreb'
impMappingOreb = require './impMappingOreb'
impMappingEdm = require './impMappingEdm'
impMappingCar = require './impMappingCar'
impMappingRESO = require './impMappingRESO'
impMappingCreaReso = require './impMappingCreaReso'
impPropFields = require './impPropFields'
propertyTagHelper = require './propertyTagHelper'
impMappingTrebManual = require './impMappingTrebManual'
getInt = PropertiesHelper.getInt
getFloat = PropertiesHelper.getFloat
debugHelper = require '../lib/debug'
debug = debugHelper.getDebugger()
impFormat = require './impFormat'
PropTranslationQueue = require './propTranslationQueue'
formatProp = impFormat.formatProp
DDF_ONLY_FIELDS = impPropFields.DDF_ONLY_FIELDS
TREB_DTA_ONLY_FIELDS= impPropFields.TREB_DTA_ONLY_FIELDS
{createSpeedMeter} = require '../lib/speed'
speedMeter = createSpeedMeter()
config = null
gTotalProcessed = {}
# 初始化翻译队列
gPropTranslationQueue = null
SRC_TYPE_TREB_MAN = 'trebMan'
SRC_TYPE_TREB = 'treb'
SRC_TYPE_BCRE_MAN= 'bcreMan'
SRC_TYPE_BCRE= 'bcre'
SRC_TYPE_RAHB = 'rahb'
SRC_TYPE_CREB = 'creb'
SRC_TYPE_OREB = 'oreb'
SRC_TYPE_EDM = 'edm'
SRC_TYPE_CAR = 'car'
SRC_TYPE_RESO = 'reso'
SRC_TYPE_BCRE_RESO = 'bcreReso'
SRC_TYPE_CREA_RESO = 'creaReso'
SRC_TYPE_DDF = 'ddf'
SRC_TYPE_DTA = 'dta'
SRC_TYPE_PROPERTY = 'property'
propSqftLib = INCLUDE './propSqft'
{findSqftAsync,getBuildingInfoAsync} = propSqftLib

statImportHelper = require './statImport'

SPCTS_DIFF_DAYS = 10  # 根据offD与当前时间间隔天数来计算spcts

addTagsAsync = functionHelper.addAsyncSupport propertyTagHelper.addtags
getBoundaryTagAsync = functionHelper.addAsyncSupport propertyTagHelper.getBoundaryTag

# for add tag to properties.
# location related tag do not need to recompute if no location change
gTagFields = Object.keys(propertyTagHelper.getPropertyAllTagFields())

OVERWRITE_FIELDS_WHEN_EMPTY = { 'sqft':1, 'vturl':1 }

#lease
#if overwrite,need to check unset fileds

#只有treb和ddf做unset。
#kit_total只有treb mannual 里有。
#trebMan字段不全，不做unset
srcToCheckUnset = {
  'treb':1,
  'ddf':1,
  'property':1,
  'bcre':1,
  'creb':1,
  'edm':1,
  'car':1,
  'reso':1,
  'bcreReso':1,
  'creaReso':1
}

# if incoming fields is empty, unset existing fields.
CHECK_UNSET_FIELDS = [
  'kch', # mapping from kit_total or kit_plus+num_kit
  'num_kit',
  'kit_plus',
  'bdrms',
  'tbdrms',
  'br_plus',
  'bthrms',
  'gr',
  'tgr',
  'park_spcs',
  'sqft',
  'sqft1',
  'sqft2',
  # NOTE: rmSqft相关字段已移除，现在由addRmSqft函数独立处理unset逻辑
  # 'rmSqft', 'rmSqft1', 'rmSqft2', 'sqftQ', 'sqftSrc' - 已移除
  'yr_built',
  'age',
  'age1',
  'age2',
  'mfee',
  'tax',
  'unt', # check if has addr.
  'apt_num', # was mapping to unt
  'flt', # front lot feet.
  'phodl',
  'psn',
  'untStorey',
  'irreg',
  'depth',
  'sldd',
  'sp',
  'dom',
  'lkr',
  'locker_lev_unit',
  'locker_num',
  'locker_unit',
  'fce',
  'bltYr', # calculated val from age
  # 'rmBltYr', # calculated val from getBuildingInfoAsync, no need to reset here
  'poss_date',
  # NOTE:对以下orig字段添加判断,防止rni数据修改后下面字段没有从col.properties中unset导致显示错误
  'origUnt',
  'origCity',
  'origAddr',
  'origCmty',
  # NOTE: 图片信息在rni删除之后，需要unset
  'media',
  'Media',
  'phoUrls',
  'thumbUrl',
  'pho',
  'lst',
  'phoLH',
  'tnLH',
  'phoP',
  'LivingAreaSF',
  'LivingAreaMetres',
  'MainLevelFinishedArea',
  'MainLevelFinishedAreaMetres',
  'UpperLevelFinishedArea',
  'UpperLevelFinishedAreaMetres',
  'BelowGradeFinishedArea',
  'BelowGradeFinishedAreaMetres'
]

###
# @description rmSqft相关字段列表，这些字段由addRmSqft函数计算生成
# 从CHECK_UNSET_FIELDS中移除，避免执行顺序问题导致字段被错误unset
###
RMSQFT_FIELDS = [
  'rmSqft',
  'rmSqft1',
  'rmSqft2',
  'sqftQ',
  'sqftSrc'
]

###
Key fields:
  ts: record created Timestamp (Date)
  mt: record changed Timestapm (Date)

  lstd:  contract/list date int (int YYYYMMDD)(change to onD)

  exp:  expirty date int (int YYYYMMDD)
  sldd:  sold date int (int YYYYMMDD)

  lp/lpr: listing price/listing price for rent (float)
  status: A/U
  lst:  last status (String)
  lat/lng/loc[]: location
  geoq: geocoding quality
  saletp: Sale/Lease
  ptype: Residential/Commercial/Other
  ptype2:
###
module.exports._config_sections = ['serverBase']
module.exports._config = _config = (cfg)->
  if not config
    config = cfg

###
 * check required fields. save error to metaInfo,
 * drop this prop if has CriticalError
 * @param {object} prop
###
module.exports.checkProp = checkProp = (prop, metaInfo, Properties)->
  metaInfo?={}
  metaInfo.criticalError?=[]

  errObj = {p:[]}
  if (not prop._id) or /null/.test prop._id
    metaInfo.criticalError.push {fld:'_id',val:prop._id}

  if 'string' isnt typeof prop.ptype
    metaInfo.criticalError.push {fld:'ptype',val:prop.ptype}

  #if cnty is Canda and prov is not in canada provlist, do not save
  if prop.cnty isnt 'CA'
    metaInfo.criticalError.push {
      fld:'cnty',
      val:prop.cnty,
      msg:'cnty is not canada'
    }
  else if not cityHelper.provMap['CA'][prop.prov]
    metaInfo.criticalError.push {
      fld:'prov',
      val:prop.prov,
      msg:'prov not in canada'
    }

  #check required fields
  for key in ['saletp', 'ptype', 'src','cnty', 'prov', 'city']
    unless prop[key]?
      metaInfo.criticalError.push {
        fld: key
        msg: "missing required fields #{key}"
      }

  # check price
  unless prop.lp? or prop.lpr?
    metaInfo.criticalError.push {
      fld: 'lp&lpr',
      msg: 'no lp and lpr'
    }
  
  # check lp and lpr with saletp
  if (/Sale/i.test prop.saletp) and (not prop.lp?)
    metaInfo.criticalError.push {
      fld: 'lp',
      msg: 'sale price is not set'
    }
  if (/Lease/i.test prop.saletp) and (not prop.lpr?)
    metaInfo.criticalError.push {
      fld: 'lpr',
      msg: 'lease price is not set'
    }

  # check loc,zip,addr
  if (not (prop.lat and prop.lng)) and (not prop.zip) and (/out of/i.test prop.uaddr)
    metaInfo.criticalError.push {
      fld: 'loc',
      msg: 'no loc and zip and out of area'
    }

  # check status,lst
  # NOTE: DDF no lst, maybe bug, remove this comment after fix
  if (not prop.status) #or (not prop.lst)
    metaInfo.criticalError.push {
      fld: 'status&lst',
      msg: 'no status or lst, status='+prop.status+':'+prop.lst
    }

  # 在市时间小于0
  if prop.dom < 0
    metaInfo.criticalError.push {
      fld: 'dom',
      msg: "dom:#{prop.dom} is less than 0"
    }
  # 上市日期大于下市日期
  if prop.onD? and prop.offD? and (prop.onD > prop.offD)
    metaInfo.criticalError.push {
      fld: 'onD&offD',
      msg: "offD:#{prop.offD} is less than onD:#{prop.onD}"
    }
  if prop.ld?
    for fld in ['sldd','cldd','cd','exp','unavail_dt']
      if prop[fld]? and ((inputToDateNum prop[fld]) < (inputToDateNum prop.ld))
        metaInfo.criticalError.push {
          fld: fld,
          msg: "#{fld}:#{prop[fld]} is less than ld:#{prop.ld}"
        }

  # check price&saletp is match
  # NOTE: @fred 暂时关闭priceInRange这个判断
  if not PropertiesHelper.isPriceInRange(prop.lp or prop.lpr,prop.saletp)
    debug.warn "price:#{prop.lp or prop.lpr} is not in range for #{prop.saletp}"
    # metaInfo.criticalError.push {
    #   fld: 'lp || lpr',
    #   msg: "price:#{prop.lp or prop.lpr} is not in range for #{prop.saletp}"
    # }

  # check TREB specific lst values
  if prop.src is 'TRB'
    # Skip import if lst is Draft
    if prop.lst is 'Draft'
      metaInfo.criticalError.push {
        fld: 'lst',
        msg: "Skip import for TRB property with lst='Draft'"
      }
    # Skip import if lst is Unavailable
    else if prop.lst is 'Unavailable'
      metaInfo.criticalError.push {
        fld: 'lst',
        msg: "Skip import for TRB property with lst='Unavailable'"
      }
    # Check properties table if lst is Previous Status
    else if prop.lst is 'Previous Status' and Properties
      existingProp = await Properties.findOne {_id: prop._id}
      if existingProp
        prop.lst = existingProp.lst
        prop.MlsStatus = existingProp.MlsStatus
        debug.info "Found existing property for TRB with lst='Previous Status', using existing lst value: #{prop.lst}"
      else
        metaInfo.criticalError.push {
          fld: 'lst',
          msg: "Skip import for TRB property with lst='Previous Status' since it doesn't exist in properties table"
        }

  delete metaInfo.criticalError unless metaInfo.criticalError.length
  return { prop, metaInfo }

###
# old里不存在的key 更新进去。  sid -> _id, DDfid->[]
# ddfid变成array，set，old有，if old is string， set to array，  is old is arry, add to set
# if duplicate with treb， will be merged with same sid.
# else will insert two different record with different ddfID.
# obj.ddfID, old.ddfID都可能是array或者number,merge并排重
#merge ddfId,orgIds, src.
###
mergeFields = (obj, old, fldName)->
  fldMapping = {}
  for value in [obj[fldName],old[fldName]]
    if Array.isArray(value)
      for v in value
        fldMapping[v] = 1
    else if value
      fldMapping[value] = 1
  keys = Object.keys(fldMapping)
  return keys if keys.length
  return null

###
merge two ohz arrays to a array
ohz1 =
[
  {
    "f": "2020-03-28 13:00",
    "t": "2020-03-28 17:00",
  },
  {
    "f": "2020-03-29 13:00",
    "t": "2020-03-29 18:00",
  }
]
ohz2 =
[
  {
    "f": "2020-03-28 14:00",
    "t": "2020-03-28 16:00"
  },
  {
    "f": "2020-03-29 16:00",
    "t": "2020-03-29 19:00",
  }
]
call mergeOhz(ohz1, ohz2), return value is:
[
  {
    "f": "2020-03-28 13:00",
    "t": "2020-03-28 17:00"
  },
  {
    "f": "2020-03-29 13:00",
    "t": "2020-03-29 19:00"
  }
]
###
mergeOhz = (ohz1, ohz2) ->
  return ohz1 if not ohz2
  return ohz2 if not ohz1
  ohz = ohz1.concat(ohz2)
  ohz.sort (a, b) ->
    return new Date(a.f) - new Date(b.f)

  results = []
  last = null
  ohz.forEach (v) ->
    if not last or (new Date(v.f) > new Date(last.t))
      last = v
      results.push last
    else if new Date(v.t) > new Date(last.t)
      last.t = v.t
      last.tp ?= v.tp

  return results

###
# @description 房源merge优先级打分
RHB   0
DDF   10
TRB   50    ON省+20,dta来源-10, Toronto/Richmond Hill/Markham/Aurora市+10
BRE   50    BC省+10
OTW   50    Ottawa市+15
CLG   50    AB省+10,Calgary市+15
EDM   50    AB省+10,Edmonton市+15
CAR   50    Mississauga/Hamilton/Burlington/Waterloo市+15
NOTE: TRB/OTW/CAR的房源,没有图片优先级低于有图片,当都为ON省都有或都没有图片时,TRB优先
没有图片-35
如果分数一致,warning log,不进行merge
###
module.exports.scoringPropMerge = scoringPropMerge = (prop)->
  score = 0
  switch prop.src
    when 'RHB'
      score += 0
    when 'DDF'
      score += 10
    when 'TRB'
      score += 50
      if prop.prov is 'ON'
        score += 20
      # Toronto/Richmond Hill/Markham/Aurora市+10
      if /(Toronto|Richmond Hill|Markham|Aurora)/i.test prop.city
        score += 10
      isDTA = (prop.trbtp?.length is 1) and (prop.trbtp[0] is 'dta')
      if isDTA
        score -= 10
      # 优先级低于CAR
      if prop.resoSrc is 'itso'
        score -= 35
      # 优先级高于OTW
      if prop.resoSrc is 'oreb'
        score += 20
    when 'BRE'
      score += 50
      if prop.prov is 'BC'
        score += 10
    when 'CLG'
      score += 50
      if prop.prov is 'AB'
        score += 10
      if /Calgary/i.test prop.city
        score += 15
    when 'OTW'
      score += 50
      if /Ottawa/i.test prop.city
        score += 15
    when 'EDM'
      score += 50
      if prop.prov is 'AB'
        score += 10
      if /Edmonton/i.test prop.city
        score += 15
    when 'CAR'
      score += 50
      if /(Mississauga|Hamilton|Burlington|Waterloo)/i.test prop.city
        score += 15
    else
      debug.error 'unknown prop source',prop
  if not prop.pho
    score -= 35
  return score

###
# @description import房源merge是否优先于old房源
# @params {object} importProp
# @params {object} oldProp
# @return string 'Yes/No/Same'
###
module.exports.isImportPropHigherPriority = isImportPropHigherPriority = (importProp,oldProp)->
  importScore = scoringPropMerge importProp
  oldScore = scoringPropMerge oldProp
  if importScore is oldScore
    debug.warn "Same source, #{importProp._id} is same with #{oldProp._id}"
    return 'Same'
  if importScore > oldScore
    return 'Yes'
  return 'No'

# 计算spcts，offD在10天以内(当前时间和下市时间)用当前时间，10天以外使用offD
calcPropSpcts = (offD) ->
  if offD
    diffDays = dayDiff offD,new Date()
    # diffDays may be NaN, when offD is invalid
    if diffDays and (diffDays > SPCTS_DIFF_DAYS)
      return inputToDate offD
  return new Date()

module.exports.addHistoryForNewProp = addHistoryForNewProp = (prop) ->
  result = {}
  if (prop.status is 'U')
    result.spcts = calcPropSpcts prop.offD # prop._mt or new Date()
  else
    result.his = []
    result.lst = prop.lst or 'New'
    result.lcTp = prop.lst
    result.spcts = new Date(
      Math.min(
        prop.ts or Date.now(),
        prop.lup or Date.now()
      )
    )
    result.his.push {
      s: result.lst,
      lp: prop.olp
      ts: new Date()#result.spcts,
      d: inputToDateNum(result.spcts)
    }
    if prop.pho
      result.his.push {
        s: 'addPic',
        ts: new Date()#result.spcts,
        d: inputToDateNum(result.spcts)
      }
  return result

###
第二步
Add Virtual tour.
add Initial photo
add open house
###
addOtherHistory = ({prop, old, result}) ->
  if old
    hisTs = prop.lup or prop.mt or prop._mt or new Date()
  else
    hisTs = new Date(
      Math.min(
        prop.ts or Date.now(),
        prop.lup or Date.now()
      )
    )
  oldM = old?.m or ''
  mIsChanged = prop.m and (Math.abs(prop.m.length - oldM.length) > 10)
  now = new Date()
  if mIsChanged
    result.his.push {s: 'chM',     ts: now, d: inputToDateNum(hisTs)}

  if prop.vturl isnt old?.vturl
    result.his.push {s: 'chVturl', ts: now, d: inputToDateNum(hisTs)}

  if prop.pho and (not old?.pho)
    result.his.push {s: 'addPic',  ts: now, d: inputToDateNum(hisTs)}
  #pull old chOh, then push new. always keep one
  if (prop.ohz or old?.ohz) and \
  ((JSON.stringify prop.ohz) isnt (JSON.stringify old?.ohz))
    # change result.his only 如果只保留最后一个记录, result.his != prop.his
    result.his = result.his.filter (his)->
      return his.s isnt 'chOh'
    result.his.push {s: 'chOh',    ts: now, d: inputToDateNum(hisTs)}
  return result

# 第一步
# NOTE： his.ts = 修改的当前时间
# spcts = state/price change timestamp, 如果用户在12-20更新了12-11 Sold 的房源，spcts = 12-20, spcts != 12-11, sldd=12-11
# spcts 用来判断推送是否应该发送，如果是新时间，推送； 如果是旧时间，不推送
# spcts 对于新房源和旧房源定义不一致，新房源=lup, 旧房源=sldd
addPriceChangeHistory = ({prop, old, result})->
  hisTs = prop.lup or prop.mt or prop._mt or new Date()
  if p12 = PropertiesHelper.isPriceChanged prop, old, prop.saletp
    # hisTs = prop.PriceChangeTimestamp or hisTs
    priceHist = {
      s: 'Pc',
      ts: new Date(),
      d: inputToDateNum(hisTs),
      o: p12[0],
      n: p12[1],
      c: (p12[1]-p12[0])
    }
    result.his.push priceHist
    result.spcts = hisTs #spcts status change ts
    result.pcts  = hisTs #pc change Ts
    result.lcTp  = 'Pc'
    result.lcO   = p12[0] # last change old value
    result.lcN   = p12[1] # last change new value
    result.pc    = result.lcN - result.lcO # new price - old price
    result.pcPct = result.pc / result.lcO
      
  if prop.sp and (old.sp isnt prop.sp) #some source does not has sp
    # hisTs = prop.sldd or hisTs
    spHist = {
      s: 'sp',
      ts: new Date(),
      d: inputToDateNum(hisTs),
      n: prop.sp
    }
    if old.sp
      spHist.o = old.sp
    if old.lst isnt prop.lst#only sp save change when lst is changed.
      result.his.push spHist
      result.spcts = hisTs #status,pc change
  return result

# 第三步
addStatusChangeHistory = ({prop, old, srcType, metaInfo, result}) ->
  isLstChange = ((old.lst?) and (prop.lst?) and (old.lst isnt prop.lst)) \
    and (prop.lst isnt 'Pc')
  #may change price and status at same time. status has higer priority.
  isStatusChange = (old.status?) and (prop.status?)\
    and (old.status isnt prop.status)
  if (prop.lst in ['New','Auc'])  and (prop.status is 'A') and \
  (old.lst in ['Sld','Pnd','Cld','Sc']) and (not prop.sp)
    metaInfo.unset.sp = 1

  # spcts 定义见#425
  if (prop.status is 'U')
    if (srcType is SRC_TYPE_TREB_MAN or srcType is SRC_TYPE_BCRE_MAN)
      result.spcts = inputToDate(prop.unavail_dt or prop.offD or prop._mt or new Date())
    else
      # NOTE: spcts = status price change timestamp, should only update when status change, ie. compare with old status/price
      # NOTE: status和price都可能会发生变化,其中一个变化时更新spcts
      if isLstChange or isStatusChange
        result.spcts = calcPropSpcts prop.offD # prop._mt or new Date()
  else
    # NOTE: 当房源状态为Active时,如果status/lst没有变化,不更新spcts
    if isLstChange or isStatusChange
      result.spcts = prop.lup or prop._mt or new Date()

  if isLstChange
    newHis ={
      s: prop.lst,
      ts: new Date()#result.spcts,
      o: old.lst,
      n: prop.lst,
    }
    if (prop.lst is 'Sld') or (prop.lst is 'Lsd')
      newHis.sldd = inputToDateNum(calcPropSpcts prop.offD)
      # 会造成ts与spcts不一致，导致spcts在其他地方使用时导致错误(watch and push)
      # newHis.ts =  prop.lup or prop.mt or new Date()

    result.his.push newHis

    #if status change,change spcts. else not change.
    result.lcTp = prop.lst #last Change
    result.lcO = old.lst # last change old status
    result.lcN = prop.lst # last change new status
    return result
  
  # Lst Change wont update status change?
  if isStatusChange
    #if status change,change spcts. else not change.
    # NOTE: 可能存在status改变,lst不变
    result.his.push {
      s: 'Chg',
      ts: new Date()#result.spcts,
      o: old.status,
      n: prop.status,
    }
    result.lcTp = 'Chg' #last Change
    result.lcO = old.status # last change old status
    result.lcN = prop.status # last change new status
    return result

# @param old  = oldWithSameId
# @param prop = incomingProp
# NOTE: addHistory for result prop
module.exports.addHistoryForOldProp = addHistoryForOldProp = (prop, old, srcType, metaInfo) ->
  old ?= {}
  result = {his:old.his or []}
  # BUG: spcts 有可能在3步都改了（如果同时改了sp,status,oh），导致spcts在其他地方使用时导致错误(watch and push)
  # NOTE: BUG已修复,在函数addStatusChangeHistory中LstChange并且为Sld/Lsd时会修改his.ts,导致spcts与his.ts不一致
  addPriceChangeHistory {prop, old,result}
  addOtherHistory {prop, old,result}
  addStatusChangeHistory {prop, old, srcType, metaInfo, result}
  if not result.his.length
    delete result.his
  return result

###
# @description 判断listing agent信息是否有效
# @param {Object|Array} listingAgentInfo - listing agent信息对象或数组
# @returns {boolean} 是否有效
###
hasListingAgentInfo = (listingAgentInfo) ->
  return false unless listingAgentInfo
  
  ###
  # @description 检查单个listing agent对象的必需字段
  # @param {Object} info - 单个listing agent信息对象
  # @returns {boolean} 返回必需字段是否都存在且有效
  ###
  hasRequiredFields = (info) ->
    return false unless info # 确保info对象存在
    
    # 检查必需的id、name、tel/eml
    hasBasicFields = info.id and info.nm and (info.tel or info.telDirect or info.eml)
    
    # 如果存在agent字段,递归检查agent对象
    if info.agnt
      # 处理agent数组情况
      if Array.isArray(info.agnt)
        return hasBasicFields and (info.agnt.every((agent) -> hasRequiredFields(agent)))
      # 处理单个agent对象情况
      return hasBasicFields and hasRequiredFields(info.agnt)
      
    return hasBasicFields
  
  # 处理数组情况
  if Array.isArray(listingAgentInfo)
    return false if (listingAgentInfo.length is 0)
    return listingAgentInfo.every(hasRequiredFields)
  
  # 如果是对象,直接检查字段
  return hasRequiredFields(listingAgentInfo)

module.exports.mergeProp = mergeProp = (propToKeep, propToMerge, metaInfo)->
  impFormat.setImportDate propToKeep, propToMerge, metaInfo
  propToKeep.trbtp = mergeFields propToMerge, propToKeep, 'trbtp'
  propToKeep.origId = mergeFields propToMerge, propToKeep, 'origId'
  propToKeep.origSrc = mergeFields propToMerge, propToKeep, 'src'
  propToKeep.ohz = mergeOhz propToMerge.ohz, propToKeep.ohz

  for k of OVERWRITE_FIELDS_WHEN_EMPTY
    if propToMerge[k]
      propToKeep[k] ?= propToMerge[k]

  if propToKeep.src isnt 'DDF' and propToMerge.src is 'DDF'
    for k of DDF_ONLY_FIELDS
      if propToMerge[k]
        propToKeep[k] = propToMerge[k]
        
  for fld in ['la', 'la2']
    if (not hasListingAgentInfo propToKeep[fld]) and (hasListingAgentInfo propToMerge[fld])
      propToKeep[fld] = propToMerge[fld]

  return propToKeep

# TODO:2024 sqft一定会存在, import时sqft需要存入sqfts_aggregate
addRmSqft = (prop,sqftCols)->
  debug.debug 'addRmSqft', prop.sqft,prop.sqft1,prop.sqft2,prop.bltYr, prop.rmBltYr
  rmSqftRet = await findSqftAsync  {
    prop,
    CollMiddle: sqftCols.SqftMiddle,
    CollAggregated: sqftCols.SqftsAggregate,
    CollQueue: sqftCols.SqftToProcess
  }
  debug.debug 'rmSqftRet',rmSqftRet
  if rmSqftRet?.rmSqft
    for fld in ['rmSqft', 'rmSqft1', 'rmSqft2', 'sqftQ']
      if rmSqftRet[fld]
        prop[fld] = rmSqftRet[fld]
    prop.sqftSrc = rmSqftRet.src

  if prop.bltYr
    delete prop.rmBltYr
  
  if sqftCols.SqftsAggregate
    buildingInfo = await getBuildingInfoAsync \
      prop, sqftCols.SqftsAggregate
  return unless buildingInfo

  prop = Object.assign prop, buildingInfo
      

getUnsetForRequiredFileds = (obj,srcType)->
  unsetFields = {}
  if srcToCheckUnset[srcType]
    for fld in CHECK_UNSET_FIELDS
      if not obj[fld]?
        unsetFields[fld]=1
  if obj.status is 'A'
    unsetFields.offD = 1
  if obj.owner
    if not obj.isEstate
      unsetFields.isEstate = 1
    if not obj.isPOS
      unsetFields.isPOS = 1
  return unsetFields

setTagsMetaInfo = (prop, metaInfo) ->
  if not prop.bndProv
    metaInfo.noBndProv = {
      msg:'no bndProv',
      prov:prop.prov,bndProv:prop.bndProv
    }
  if not prop.bndCity #geocoding lat，lng不在boundary里面。
    metaInfo.noBndCity = {
      msg:'no bndcity',
      prov:prop.city,
      bndCity:prop.bndCity
    }

  if prop.bndProv?.nm isnt prop.prov
    metaInfo.bndProv = {
      msg:'bndProv isnt prov',
      prov:prop.prov,
      bndProv:prop.bndProv
    }

  if (bndCity = prop.bndCity) and (bndCity?.nm isnt prop.city)
    metaInfo.bndCity={city:prop.city,bndCity:bndCity}

  if prop.bndCmty and (prop.bndCmty?.nm isnt prop.cmty)
    metaInfo.bndCmty={cmty:prop.cmty,bndCmty: prop.bndCmty}
  return prop

#unset tags if no new tag get
copyTags = ({tags, set}) ->
  for key in gTagFields
    set[key]= tags[key] if tags[key]?

unsetTags = ({tags, unset, set}) ->
  for key in gTagFields
    #没找到就unset
    if not tags[key]?
      delete set[key] if set[key]
      unset[key] = 1

###
# @description 处理sid字段，支持origSid
# @param {Object} branch - 查询分支对象
###
applySidCondition = (branch,obj) ->
  if obj.origSid
    branch.sid = { $in: [obj.sid, obj.origSid] }
  else
    branch.sid = obj.sid

###
# @description 统一应用通用查询条件到分支
# @param {Object} branch - 查询分支对象
# @param {Object} obj - 房源对象
###
applyCommonConditions = (branch,obj) ->
  # 添加价格条件 (lp/lpr)
  for field in ['lp', 'lpr']
    if obj[field]
      branch[field] = obj[field]
    else
      branch[field] = null

  # 添加单元号条件 (unt)
  if obj.unt?
    branch.unt = '' + obj.unt
  else
    branch.unt = null

  # 添加src字段条件
  if obj.src in ['TRB', 'BRE']
    branch.src = { $ne: obj.src }

  # 特殊处理：没有图片时去掉TRB限制
  if (obj.src is 'TRB') and (not obj.pho)
    delete branch.src

  # 添加总面积条件
  if obj.tot_area
    branch.tot_area = obj.tot_area


###
# @description 构建房源merge查询条件
# @param {Object} obj - 房源对象，包含sid、prov、uaddr等字段
# @param {Boolean} isRHBNotFound - 是否为RHB房源未找到的情况
# @param {Boolean} shouldExpandOnDRange - 是否扩大onD时间范围
# @returns {Object} MongoDB查询条件对象
# @note 重构后的查询结构：基础条件在$or外部，区分性字段在$or内部
###
buildQuery = (obj, isRHBNotFound = false, shouldExpandOnDRange = false) ->
  onDTime = inputToDate(obj.onD).getTime()

  # 计算时间范围
  timeRanges = calculateTimeRanges(onDTime, shouldExpandOnDRange, obj.pho)

  # 基础查询条件（所有策略共用，只包含核心字段）
  baseConditions = {
    _id: { $ne: obj._id }      # 排除当前记录
    merged: null               # 未合并记录
    del: null                  # 未删除记录
    ptype: obj.ptype          # 相同属性类型
  }

  # 策略1: sid + uaddr匹配（只包含sid、uaddr、onD三个字段）
  sidUaddrQuery = Object.assign {}, baseConditions, {
    uaddr: obj.uaddr
    onD: { $gte: timeRanges.halfMonth.from, $lte: timeRanges.halfMonth.to }
  }
  applySidCondition(sidUaddrQuery,obj)

  # 策略2: sid + prov匹配（包含完整条件）
  sidProvQuery = Object.assign {}, baseConditions,{
    prov: obj.prov
    onD: { $gte: timeRanges.halfMonth.from, $lte: timeRanges.halfMonth.to }
  }
  applySidCondition(sidProvQuery,obj)
  applyCommonConditions(sidProvQuery,obj)

  # 策略3: status + uaddr匹配（包含完整条件，包括tot_area）
  uaddrStatusQuery = Object.assign {}, baseConditions,{
    uaddr: obj.uaddr
    status: obj.status
  }

  # 添加时间范围条件
  timeRange = if obj.pho then timeRanges.base else timeRanges.noPhoto
  uaddrStatusQuery.onD = { $gte: timeRange.from, $lte: timeRange.to }

  # 应用通用条件（包括tot_area）
  applyCommonConditions(uaddrStatusQuery,obj)

  # 特殊处理：Ontario下的RHB房源merge不考虑status
  # NOTE:Ontario下的RHB房源merge不考虑status，查找是否存在treb的同一房源
  if (obj.src is 'RHB') and (obj.prov is 'ON')
    delete uaddrStatusQuery.status
    uaddrStatusQuery.src = 'TRB'

  # RHB房源未找到时强制添加status条件
  # NOTE:如果Ontario下的RHB房源没有找到TRB房源merge，需要考虑status查找其它类型房源(DDF)
  if isRHBNotFound
    delete uaddrStatusQuery.src
    uaddrStatusQuery.status = obj.status

  return {sidUaddrQuery, sidProvQuery, uaddrStatusQuery}


###
# @description 查找用于合并的旧房源记录
# 采用三层查询策略，按优先级顺序查找匹配的房源：
# 1. sid + uaddr 匹配（最高优先级）- 相同sid和地址的房源
# 2. sid + prov 匹配（中等优先级）- 相同sid和省份的房源
# 3. status + uaddr 匹配（最低优先级）- 相同状态和地址的房源
#
# @param {Object} Properties - MongoDB Properties集合对象
# @param {Object} obj - 待查找的房源对象，必须包含以下字段：
#   - _id: 房源ID
#   - sid: 房源系统ID
#   - uaddr: 统一地址
#   - prov: 省份
#   - status: 房源状态
#   - onD: 上市日期
#   - ptype: 房源类型
#   - src: 数据源（如'RHB', 'TRB', 'DDF'等）
# @param {Boolean} isRHBNotFound - 是否为RHB房源未找到的情况，默认false
#   当为true时，会在uaddr+status查询中强制添加status条件
# @param {Boolean} shouldExpandOnDRange - 是否扩大onD时间范围，默认false
#   当为true时，会使用更大的时间范围进行查询
# @returns {Object|undefined} 找到的房源对象，如果未找到则返回undefined
###
findOldPropForMerge = (Properties, obj, isRHBNotFound = false, shouldExpandOnDRange = false) ->
  # 构建三种查询策略的查询条件
  {sidUaddrQuery, sidProvQuery, uaddrStatusQuery} = buildQuery(obj, isRHBNotFound, shouldExpandOnDRange)

  # 策略1: 优先查找相同sid和地址的房源（最精确匹配）
  # 这种匹配通常用于同一房源在不同时间的更新
  sidUaddrProp = await Properties.findOne(sidUaddrQuery)
  return sidUaddrProp if sidUaddrProp

  # 策略2: 查找相同sid和省份的房源（中等精确度）
  # 用于处理地址可能有变化但sid相同的情况
  sidProvProp = await Properties.findOne(sidProvQuery)
  return sidProvProp if sidProvProp

  # 策略3: 查找相同状态和地址的房源（最宽泛匹配）
  # 用于处理sid可能不同但实际是同一房源的情况
  uaddrStatusProp = await Properties.findOne(uaddrStatusQuery)
  return uaddrStatusProp if uaddrStatusProp

  return

# 计算时间范围的辅助函数
calculateTimeRanges = (onDTime, shouldExpandOnDRange, hasPho) ->
  onDMergeDateSpanForDiffSid = config?.serverBase?.onDMergeDateSpanForDiffSid or (24 * 3600000)
  onDMergeDateSpanForSameSid = config?.serverBase?.onDMergeDateSpanForSameSid or (15 * 24 * 3600000)
  onDMergeDateSpanForDiffSidAndNoPho = config?.serverBase?.onDMergeDateSpanForDiffSidAndNoPho or (7 * 24 * 3600000)

  baseSpan = if shouldExpandOnDRange
    config?.serverBase?.onDRangeExpansionValue or (5 * 24 * 3600000)
  else
    onDMergeDateSpanForDiffSid

  {
    halfMonth: {
      from: inputToDateNum(new Date(onDTime - onDMergeDateSpanForSameSid))
      to: inputToDateNum(new Date(onDTime + onDMergeDateSpanForSameSid))
    }
    base: {
      from: inputToDateNum(new Date(onDTime - baseSpan))
      to: inputToDateNum(new Date(onDTime + baseSpan))
    }
    noPhoto: {
      from: inputToDateNum(new Date(onDTime - onDMergeDateSpanForDiffSidAndNoPho))
      to: inputToDateNum(new Date(onDTime + onDMergeDateSpanForDiffSidAndNoPho))
    }
  }

shouldResetMzh = (resultProp, incomingProp) ->
  if not resultProp.m_zh
    return false
  if not incomingProp.m
    return true
  if resultProp.m isnt incomingProp.m
    return true
  return false

# TODO: +documents, updateOldProp-> did not updated, just returned the old prop with modified fields
updateOldProp = ({
  incomingProp,
  oldProp,
  metaInfo,
  srcType,
  noTag,
  School,
  Boundary,
  TransitStop,
  transitCities,
  Census2016,
  Census2016ReversKeys,
  Census2021,
  sqftCols
}) ->
  resultProp = Object.assign {}, oldProp
  # remove m_zh if m is changed
  if shouldResetMzh(resultProp,incomingProp)
    debug.info 'update m_zh', resultProp._id, incomingProp.m
    resultProp.m_zh = ''
    # 添加到翻译队列
    try
      if not gPropTranslationQueue
        gPropTranslationQueue = new PropTranslationQueue()
      await gPropTranslationQueue.addTranslationTask(resultProp._id, resultProp)
    catch error
      debug.error 'Failed to add translation task in updateOldProp', error, resultProp._id
  
  # srcType is SRC_TYPE_DTA but old prop trbtp NOT only has dta update TREB_DTA_ONLY_FIELDS only
  # else update all fields because that mean update same prop from same source
  oldPropTrbtpOnlyHasDta = oldProp.trbtp?[0] is 'dta' and oldProp.trbtp?.length is 1
  if (srcType is SRC_TYPE_DTA) and (not oldPropTrbtpOnlyHasDta)
    for k of TREB_DTA_ONLY_FIELDS
      if incomingProp[k]
        resultProp[k] = incomingProp[k]
  else
    # update all fields
    Object.assign resultProp, incomingProp
    unset = getUnsetForRequiredFileds incomingProp, srcType
    Object.assign metaInfo.unset, unset
  
  histories = addHistoryForOldProp incomingProp, oldProp, srcType, metaInfo
  Object.assign resultProp, histories

  # evow ,idx and dta have sameId but different trbtp, need merge them
  resultProp.trbtp = mergeFields incomingProp, oldProp, 'trbtp'

  tagParms = {
    prop: incomingProp,
  }

  # lat/lng changed, need update related tags
  isNeedUpdateAllTags = (not noTag) and \
  (not isSameCoordinate incomingProp.lat, incomingProp.lng, oldProp.lat, oldProp.lng)

  if isNeedUpdateAllTags
    tagParms.School = School
    tagParms.Boundary = Boundary
    tagParms.TransitStop = TransitStop
    tagParms.transitCities = transitCities
    tagParms.Census2016 = Census2016
    tagParms.Census2016ReversKeys = Census2016ReversKeys
    tagParms.Census2021 = Census2021

  try
    # 因为 base tags (sldDifPct, sldDom, bltYr1, bltYr2, bltYr) 一定要每次更新，即便位置没变也要跑一次 addTags
    tags = await addTagsAsync tagParms
  catch err
    debug.error err

  if tags
    copyTags { tags, set: resultProp }
    if isNeedUpdateAllTags
      setTagsMetaInfo resultProp, metaInfo
      unsetTags { tags, unset: metaInfo.unset, set: resultProp } if srcToCheckUnset[srcType]

  if sqftCols and (incomingProp.sqft isnt oldProp.sqft or incomingProp.m isnt oldProp.m)
    await addRmSqft resultProp, sqftCols

    # 单独检查这些字段是否需要unset，避免执行顺序问题
    if srcToCheckUnset[srcType]
      for fld in RMSQFT_FIELDS
        if not resultProp[fld]?
          metaInfo.unset[fld] = 1

  return resultProp

createNewProp = ({
  incomingProp,
  metaInfo,
  srcType,
  noTag,
  School,
  Boundary,
  TransitStop,
  transitCities,
  Census2016,
  Census2016ReversKeys,
  Census2021,
  sqftCols
}) ->
  resultProp = Object.assign {}, incomingProp
  histories = addHistoryForNewProp incomingProp
  Object.assign resultProp, histories

  tagParms = {
    prop: resultProp,
    School,
    Boundary,
    TransitStop,
    transitCities,
    Census2016,
    Census2016ReversKeys,
    Census2021,
  }
  try
    tags = await addTagsAsync tagParms
  catch err
    debug.error err

  if tags
    copyTags { tags, set: resultProp }
    setTagsMetaInfo resultProp, metaInfo
  
  await addRmSqft resultProp, sqftCols if sqftCols

  # 添加到翻译队列（如果有需要翻译的内容）
  if resultProp.m
    try
      if not gPropTranslationQueue
        gPropTranslationQueue = new PropTranslationQueue()
      await gPropTranslationQueue.addTranslationTask(resultProp._id, resultProp)
    catch error
      debug.error 'Failed to add translation task in createNewProp', error, resultProp._id

  return resultProp

module.exports.findDuplicatePropAndDeterminePriority = findDuplicatePropAndDeterminePriority = ({
  prop,
  srcType,
  metaInfo,
  Properties,
  PropertiesImportLog,
}) ->
  # merge需要过滤RM房源
  if prop.src is 'RM'
    return { propToKeep:prop }
  propToKeep = null
  propToMerge = null

  # DDF Board = 82, 85, 86 時, 此房源應是來自 TREB, 把 sid 加上 'TRB' 前綴應該就是對應的 TREB 房源 id
  # 如果找得到此 TREB 房源就以此作為 merge 對象, 找不到才使用傳統的 uaddr, onD, ... 等字段比對尋找 merge 對象
  # DDF_TRB_BOARD_NUMS = [ 82, 85, 86, '82', '85', '86' ]
  # 取消board判断，改为prov === 'ON'判断,如果DDF房源省份为ontario时,先根据sid查找TREB房源
  # 需求变更文档: docs/Change_requirements/20240717_fix_propCity.md 解决方案#2
  # TODO: OTW/CLG/BRE/RHB 具体的sid对应与merge逻辑,需要确认sid能否对应上
  PROV_ONTARIO = 'ON' # 房源已经进行过formatProvAndCity,所以prov使用的是abbreviation
  if (prop.src is 'DDF') and (prop.prov is PROV_ONTARIO) and prop.sid
    oldProp = await Properties.findOne { _id: "TRB#{prop.sid}", prov:PROV_ONTARIO }

  if not oldProp
    oldProp = await findOldPropForMerge(Properties,prop)

  # RHB房源优先merge到treb房源,如果没有查询其它房源(DDF)进行merge
  if (prop.src is 'RHB') and (not oldProp)
    oldProp = await findOldPropForMerge(Properties,prop,true)
  
  if not oldProp
    isRHB = prop.src is 'RHB'
    oldProp = await findOldPropForMerge(Properties,prop,isRHB,true)

  # not found the old prop, nothing todo
  if not oldProp
    return { propToKeep:prop, propToMerge }
  # if prop and oldProp are both from TRB, and neither of them has photo, keep the old one, merge new prop
  if (prop.src is 'TRB') and (oldProp.src is 'TRB') and (not prop.pho) and (not oldProp.pho)
    return { propToKeep:oldProp, propToMerge:prop }

  isImportPropHigh = isImportPropHigherPriority prop, oldProp

  # same priority, keep both, do not merge
  if isImportPropHigh is 'Same'
    return { propToKeep:prop, propToMerge }

  # impoted new prop higher priority, keep new, merge old
  if isImportPropHigh is 'Yes'
    propToKeep = prop
    propToMerge = oldProp
    metaInfo.setOldAsMerge = oldProp._id
  else
    propToKeep = oldProp
    propToMerge = prop
    metaInfo.setNewAsMerge = 1
  return { propToKeep, propToMerge }

isDraftSid = (sid='')->
  return /draft/i.test(sid)

pushOrigSidToASIDs = (prop,aSIDs = [])->
  if prop.origSid and (not isDraftSid(prop.origSid))
    aSIDs.push {sid:prop.origSid,id:prop._id}
  return aSIDs

# merge aSIDs and remove duplicate sid
mergeAndRemoveDupASIDs = (aSIDsA = [],aSIDsB = [])->
  aSIDs = aSIDsA.concat aSIDsB
  uniqueKeys = new Set()
  result = []
  for obj in aSIDs
    key = "#{obj.sid}_#{obj.id}"
    if not uniqueKeys.has(key)
      uniqueKeys.add(key)
      result.push obj
  return result

module.exports.mergePropAndSaveToDB = mergePropAndSaveToDB = ({
  propToKeep,
  propToMerge,
  metaInfo,
  Properties,
}) ->
  if propToKeep
    propToKeep = await mergeProp propToKeep, propToMerge, metaInfo
    propToMerge.merged = propToKeep._id
    if propToKeep.aSIDs
      propToKeep.aSIDs.push {sid:propToMerge.sid,id:propToMerge._id}
    else
      propToKeep.aSIDs = [
        {sid:propToMerge.sid,id:propToMerge._id},
        {sid:propToKeep.sid,id:propToKeep._id}
      ]
      propToKeep.aSIDs = pushOrigSidToASIDs propToKeep,propToKeep.aSIDs
      propToKeep.aSIDs = pushOrigSidToASIDs propToMerge,propToKeep.aSIDs
    
    propToKeep.aSIDs = mergeAndRemoveDupASIDs propToKeep.aSIDs,propToMerge.aSIDs

    # NOTE: 当uaddr改变时,func->removePropFromMergeChain将merged/mergedTop放在metaInfo.unset中
    # 此时添加了新的merged需要将unset中的merged字段删除,metaInfo中添加了lastMerged
  unsetOfPropToKeep = {}
  unsetOfPropToMerge = {}
  # unset 要做在这次进来的 incoming prop 上，metaInfo.idBeforeMerge 会纪录这个 property _id
  if metaInfo.idBeforeMerge is propToKeep?._id
    unsetOfPropToKeep = Object.assign {},metaInfo.unset
  else
    unsetOfPropToMerge = Object.assign {},metaInfo.unset
    delete unsetOfPropToMerge.merged

  await updatePropertyToDB {
    _id: propToMerge._id,
    set: propToMerge,
    unset: unsetOfPropToMerge,
    Properties,
  }

  if propToKeep
    await updatePropertyToDB {
      _id: propToKeep._id,
      set: propToKeep,
      unset: unsetOfPropToKeep,
      Properties,
    }

    # if chain merge exists, update mergedTop
    await Properties.updateMany \
    { merged: propToMerge._id }, \
    { $set: { mergedTop: propToKeep._id }}

###
if we have 3 props A, B and C, and A merged to B, B merged to C, (A -> B -> C)
now we want to remove prop B from this chain, (change to A -> C)
you can call this function with params {prop: B, metaInfo, Properties}
###
module.exports.removePropFromMergeChain = removePropFromMergeChain = (
{
  prop,
  metaInfo,
  Properties
}) ->
  if prop.merged
    await Properties.updateMany \
      { merged: prop._id }, \
      { $set: { merged: prop.merged }}
    
    # remove related sid from merged prop
    update = {
      $pull:{
        aSIDs:{id:prop._id},
        origId:prop._id
      }
    }
    await Properties.updateOne {_id:prop.merged},update

    metaInfo.lastMerged = prop.merged
    delete prop.merged
    delete prop.mergedTop
    metaInfo.unset.merged = 1
    metaInfo.unset.mergedTop = 1
  else
    propMergedToThis = await Properties.findOne { merged: prop._id }, { _id: 1 }
    if propMergedToThis
      await Properties.updateMany \
        { merged: prop._id }, \
        { $set: { merged: propMergedToThis._id }}

      await Properties.updateMany \
        { mergedTop: prop._id }, \
        { $set: { mergedTop: propMergedToThis._id }}

      ###
      if merge chain is (A -> B -> C -> D), and we want to remove D, new mergedTop will be C,
      we need to make sure fields 'merged' and 'mergedTop' of C must to be unset
      ###
      await Properties.updateOne { _id: propMergedToThis._id }, {
        $unset: { merged: 1, mergedTop: 1 }
      }
  return prop

###
find duplicate props, merge, save record, save log as well
###
module.exports.saveToMongo = saveToMongo = ({
  Properties,
  prop,
  metaInfo,
  TransitStop,
  transitCities,
  School,
  Boundary,
  Census2016,
  Census2016ReversKeys,
  Census2021,
  Census,
  srcType,
  PropertiesImportLog,
  noTag,
  sqftCols,
  oldWithSameId, # 新增参数，避免重复查询
  hasQueriedVow # 新增参数，避免重复查询, 标明已经找过oldWithSameId, 防止oldWithSameId未找到再次查找的问题
}, cb) ->
  metaInfo.unset ?= {}
  propToKeep = null
  propToMerge = null
  try
    debug.debug 'saveToMongo', prop._id
    prop.ts ?= inputToDate(prop.onD) or new Date()

    # 生成rmPsn和rmPsnDate字段，用于房源PSN搜索
    # 只要房源有psn字段就生成rmPsn相关字段，不再限制为TREB来源
    psnStr = prop.psn?.toString() or ''
    psnStr += ',' + prop.poss_type.toString() if prop.poss_type
    if psnStr
      { rmPsn, rmPsnDate } = PropertiesHelper.genRmPsn {onD:prop.onD, psn:psnStr}
      if rmPsn?.length > 0
        prop.rmPsn = rmPsn
      else
        metaInfo.unset.rmPsn = 1
      if rmPsnDate
        prop.rmPsnDate = rmPsnDate
      else
        metaInfo.unset.rmPsnDate = 1

    # 如果已经找过oldWithSameId，就不再查询
    if (not oldWithSameId) and (not hasQueriedVow)
      oldWithSameId = await Properties.findOne { _id: prop._id }
    
    if oldWithSameId
      updatedProp = await updateOldProp {
        incomingProp: prop,
        oldProp: oldWithSameId,
        metaInfo,
        srcType,
        noTag,
        School,
        Boundary,
        TransitStop,
        transitCities,
        Census2016,
        Census2016ReversKeys,
        Census2021,
        sqftCols
      }
      # debug.info 'saveToMongo oldWithSameId',prop._id or prop.id #,' picNum:',prop.picNum,' updatedProp:',updatedProp.picNum
      speedMeter.check {oldWithSameId:1}
      statImportHelper.updateStats {board:srcType,type:'update',update:updatedProp,unset:metaInfo.unset}
      # NOTE: pho添加,需要去掉merged重新查找merge
      if (updatedProp.uaddr isnt oldWithSameId.uaddr) or (updatedProp.pho and (not oldWithSameId.pho))
        # addr changed, if it's merged prop, may need to unmerge
        updatedProp = await removePropFromMergeChain {
          prop: updatedProp,
          metaInfo,
          Properties,
        }

        # addr changed 之后，可能又可以跟另一个 merge 了，要再重找一次
        { propToKeep, propToMerge } = await findDuplicatePropAndDeterminePriority({
          prop: updatedProp,
          srcType,
          metaInfo,
          Properties,
          PropertiesImportLog,
        })
      else if updatedProp.mergedTop
        propToMerge = updatedProp
        propToKeep = await Properties.findOne { _id: updatedProp.mergedTop }
      else if updatedProp.merged
        propToMerge = updatedProp
        propToKeep = await Properties.findOne { _id: updatedProp.merged }
      else
        # TODO: 当merge逻辑发生改变时，已经存在的房源不会进行merge操作
        # { propToKeep, propToMerge } = await findDuplicatePropAndDeterminePriority({
        #   prop: updatedProp,
        #   srcType,
        #   metaInfo,
        #   Properties,
        #   PropertiesImportLog,
        # })
        propToKeep = updatedProp
    else
      debug.info 'saveToMongo new ID',prop._id or prop.id #,' picNum:',prop.picNum
      newProp = await createNewProp {
        incomingProp: prop,
        metaInfo,
        srcType
        noTag,
        School,
        Boundary,
        TransitStop,
        transitCities,
        Census2016,
        Census2016ReversKeys,
        Census2021,
        sqftCols
      }
      statImportHelper.updateStats {board:srcType,type:'new',update:newProp,unset:metaInfo.unset}

      { propToKeep, propToMerge } = await findDuplicatePropAndDeterminePriority({
        prop: newProp,
        srcType,
        metaInfo,
        Properties,
        PropertiesImportLog,
      })
    # debug.info 'propToKeep: ',propToKeep
    if propToKeep?._id is prop._id \
    and (srcType is SRC_TYPE_TREB_MAN or srcType is SRC_TYPE_BCRE_MAN) \
    and (propToKeep.status is 'U')
      propToKeep.mt = inputToDate(
        propToKeep.unavail_dt or
        propToKeep.offD or
        propToMerge?.mt or
        propToKeep.mt
      )

    if propToMerge
      await mergePropAndSaveToDB {
        propToKeep,
        propToMerge,
        metaInfo,
        Properties,
      }
    else if propToKeep
      await updatePropertyToDB {
        _id: propToKeep._id,
        set: propToKeep,
        unset: metaInfo.unset,
        Properties,
      }
  
    await saveMetaInfo { _id: prop._id, srcType, metaInfo, PropertiesImportLog }
    return cb()
  catch error
    debug.error error
    return cb error

###
  save update prop, update main prop. unset required fields.save log
###
updatePropertyToDB=(
  {
    _id,
    set,
    unset={},
    Properties,
  })->
    return unless set
    for fld in ['unt','st_num','apt_num']
      if set[fld]?
        set[fld] = set[fld].toString()

    delete set.noGeocoding
    delete set.upsert #from trebman

    update = {
      $set: set
      $setOnInsert: {}
      $unset: unset
    }

    if Object.keys(update.$unset)?.length>0
      for k,v of update.$unset
        delete update.$set[k]

    if update.$set.ts
      update.$setOnInsert.ts = update.$set.ts
      delete update.$set.ts

    await Properties.updateOne { _id }, update, { upsert:true }

saveMetaInfo = ({srcType,_id,metaInfo,PropertiesImportLog})->
  return if Object.keys(metaInfo).length is 0
  return if not PropertiesImportLog
  metaInfo._id = "#{metaInfo.idBeforeMerge}_#{srcType}"
  metaInfo.ts = new Date()
  metaInfo.id = _id
  log = metaInfo.lstlog
  delete metaInfo.lstlog
  delete metaInfo.unset if metaInfo.unset and (Object.keys(metaInfo.unset) is 0)
  return await PropertiesImportLog.updateOne \
    { _id: metaInfo._id }, \
    { $set: metaInfo, $push: {logs:{$each:[log], $slice:-200}}}, \
    { upsert: true }

###
@param {object} item = prop
if keepPropGeocoding or out of area, do not do geocoding
###
# TODO: functional programming, instead of change item in place, return new item
module.exports.doGeoCoding = doGeoCoding = (GeoCoder, item, metaInfo, cb) ->
  if item.keepPropGeocoding
    item.keepQ = 1
    return cb(null,{msg:'keepPropGeocoding'})

  if item.noGeocoding
    return cb(null,{msg:'skiped'})

  if /OUT OF/.test(item?.uaddr)
    debug.debug "skip geocoding for #{item._id}, #{item.uaddr}"
    return cb(null,{msg:'skiped'})
    
  unless GeoCoder
    return cb 'Error: doGeoCoding: no geocoder, run batch with preload-model?'

  GeoCoder.geocoding item, (err, geoResult) ->
    return cb err if err
    return cb null, {msg:'no geoResult'} unless geoResult

    if geoResult?.err
      debug.error "\nGeoCode ERROR [#{item._id}]\
        :#{JSON.stringify(geoResult.err)}"
      metaInfo.geocodingError = err

    # item not geoed before or geo-result better than orig
    # geoResult.q == item.geoq == 100
    if (not item.geoq) or (geoResult.q > item.geoq)
      for k in ['lat', 'lng', 'faddr']
        item[k] = geoResult[k] or item[k]

      if item.geoq and (item.geoq isnt geoResult.q)
        metaInfo.geoq = { orig: item.geoq, new: geoResult.q }
      item.geoq = geoResult.q

      item.zip ?= geoResult.zip if geoResult.zip

    # 更新房源的uaddr
    item.uaddr = geoResult._id if geoResult._id

    # item orig lat/lng from 3rd party, change this loc anyway using item.lat
    # NOTE: this has bug, originally from 3rd party wrong lat, lng; db.mls_treb_master_records.find({_id:'TRBC4795612'})
    if item.lat and item.lng
      item.loc = { type: 'Point', coordinates: [item.lng, item.lat]}

    #save to import log
    # when useprop and prop is not saved,geoResult is input, no _id
    # BUG: only cached has value
    for fld in ['useprop','cached','noGeocoding','doCoding','fixAddr']
      if geoResult[fld]
        debug.debug "#{fld}[#{item._id}] geocoder: #{geoResult.src}, \
          geoq: #{geoResult.q}\
          geoResult id:#{geoResult._id}"
        metaInfo.geocoding ?= {}
        metaInfo.geocoding[fld] = true
    cb(null, geoResult)


getConvertParams = (srcType)->
  switch srcType
    when SRC_TYPE_BCRE_MAN
      return impMappingBcreManual.convertFromBcre
    when SRC_TYPE_BCRE
      return impMappingBcre.convertFromBcre
    when SRC_TYPE_TREB,SRC_TYPE_DTA
      return impMappingTreb.convertFromTreb
    when SRC_TYPE_DDF
      return impMappingDdf.convertFromDDF
    when SRC_TYPE_PROPERTY
      return impMappingProperty.convertFromProperty
    when SRC_TYPE_TREB_MAN
      return impMappingTrebManual.convertFromTrebManual
    when SRC_TYPE_RAHB
      return impMappingRahb.convertFromRahb
    when SRC_TYPE_CREB
      return impMappingCreb.convertFromCreb
    when SRC_TYPE_OREB
      return impMappingOreb.convertFromOreb
    when SRC_TYPE_EDM
      return impMappingEdm.convertFromEdm
    when SRC_TYPE_CAR
      return impMappingCar.convertFromCar
    when SRC_TYPE_RESO
      return impMappingRESO.convertFromRESO
    when SRC_TYPE_BCRE_RESO
      return impMappingBcreReso.convertFromBcreReso
    when SRC_TYPE_CREA_RESO
      return impMappingCreaReso.convertFromCreaReso
    else
      return null

isSameCoordinate = (lat1, lng1, lat2, lng2) ->
  if not (lat1 and lng1 and lat2 and lng2)
    return false
  return geolib.getDistance(
    { latitude: lat1, longitude: lng1 },
    { latitude: lat2, longitude: lng2 }
  ) <= 5

checkProvAndCity = ({prop,ProvCityCmty,Boundary})->
  if not prop
    debug.error 'checkProvAndCity: prop is null'
    return
  
  if prop.city?.toUpperCase() is 'NO CITY VALUE'
    debug.warn "propId:#{prop._id} city is 'No City Value', skip checkProvAndCity"
    return

  # 从Boundary中检查房源的prov和city是否匹配
  if Boundary
    # 如果有经纬度信息，从Boundary查找
    if prop.lat? and prop.lng?
      query={tp:'city'}
      query['bnds.features.geometry'] =
        $geoIntersects:
          $geometry:
            'type' : 'Point',
            'coordinates' : [prop.lng,prop.lat]
      records = await Boundary.findToArray query, {projection: {prov:1,city:1}}
      isSame = false
      for record in records
        if (record.city.toUpperCase() is prop.city.toUpperCase()) and (record.prov is prop.prov)
          isSame = true
          break
      if not isSame
        debug.warn "propId:#{prop._id} prov:#{prop.prov} is not same with Boundary, city:#{prop.city}"
    else
      provs = await Boundary.distinct 'prov',{tp:'city',city:prop.city}
      if not provs.includes(prop.prov)
        debug.warn "propId:#{prop._id} prov:#{prop.prov} is not in Boundary provs:#{provs}, city:#{prop.city}"
  # 从ProvCityCmty中检查房源的prov和city是否匹配
  if ProvCityCmty
    provs = await ProvCityCmty.distinct '_id.prov',{'_id.city': new RegExp("^#{prop.city}$", 'i')}
    # 对provs数组中的每个省份名称进行标准化缩写处理
    provs = provs.map (prov)-> cityHelper.getProvAbbrName(prov)
    # 如果房源的prov不在provs数组中，则认为房源的prov信息不准确，warning log
    if not provs.includes(prop.prov)
      debug.warn "propId:#{prop._id} prov:#{prop.prov} is not in ProvCityCmty provs:#{provs}, city:#{prop.city}"
  return

# 设置带有经纬度房源的geoq,boundaryTags
module.exports.setPropGeoQAndBoundaryTags = setPropGeoQAndBoundaryTags = ({
  prop = {},
  Boundary,
  GeoCoder,
  Properties,
  oldProp,
  hasQueriedVow
})->
  ret = await shouldSkipGeoCoding {prop, Boundary, Properties, oldProp,hasQueriedVow}
  if ret?.isSkip
    if not (prop.lat and prop.lng)
      debug.error "skip geoCoding but prop.lat and prop.lng is not set, propId:#{prop._id}"
      return
    # 跳过geoCoding时需要判断geoCache是否存在,存在时使用geoCache._id作为uaddr
    if GeoCoder
      # 使用unifyAddress函数获取uaddrZip
      uaddrZip = unifyAddress prop, true
      geocache = await GeoCoder.getGeoCacheByUaddr prop.uaddr, uaddrZip
      if geocache
        prop.uaddr = geocache._id
    else
      debug.warn 'setPropGeoQAndBoundaryTags no GeoCoder'
    prop.keepPropGeocoding = true
    prop.geoq = ret.geoQ
    prop.loc = {type:'Point',coordinates:[prop.lng,prop.lat]}
    addBoundaryInfo prop,ret.boundaryTags

###
# @description 检查地址相关字段是否发生变化,如果oldProp没有经纬度信息则认为发生变化,返回true
# @param {Object} prop - 新的房源对象
# @param {Object} oldProp - 旧的房源对象
# @returns {Boolean} 如果地址相关字段(省份、城市、地址、邮编)发生变化则返回true
###
isAddressFieldsChanged = (prop, oldProp) ->
  # 如果oldProp没有经纬度信息认为发生变化,返回true
  if not (oldProp?.lng and oldProp?.lat)
    return true
  return oldProp.prov isnt prop.prov or \
         oldProp.city isnt prop.city or \
         oldProp.addr isnt prop.addr or \
         oldProp.zip isnt prop.zip

###
# @description 对rni中含有loc信息的房源进行geoq检查,判断是否需要跳过geoCoding
# @param {Object} prop - 房源对象
# @param {Object} Boundary - Boundary collection
# @param {Object} Properties - Properties collection
# @param {Object} [oldProp] - 从Properties collection中查询到的老房源对象，避免重复查询
# @return {Object} ret
#   @property {Boolean} isSkip - 是否跳过geoCoding
#   @property {Number} [geoQ] - geoq值
#   @property {Object} [boundaryTags] - boundary tag信息(bndCity,bndProv,bndSubCity,bndRegion,bndCmty)
###
shouldSkipGeoCoding = ({prop = {}, Boundary, Properties, oldProp, hasQueriedVow = false})->
  # 如果已经传入了oldProp，就不再查询Properties
  if oldProp
    # 地址相关字段没有发生变化,跳过geoCoding
    if not isAddressFieldsChanged(prop, oldProp)
      debug.debug "id:#{prop._id} address info not changed, need skip"
      prop.lat = oldProp.lat
      prop.lng = oldProp.lng
      prop.uaddr = oldProp.uaddr
      return {isSkip:true, geoQ:oldProp.geoq}
  # 如果没有找过oldProp，并且Properties存在，则查询oldProp
  else if Properties and (not hasQueriedVow)
    oldProp = await Properties.findOne {_id:prop._id}
    if not isAddressFieldsChanged(prop, oldProp)
      debug.debug "id:#{prop._id} address info not changed, need skip"
      prop.lat = oldProp.lat
      prop.lng = oldProp.lng
      prop.uaddr = oldProp.uaddr
      return {isSkip:true, geoQ:oldProp.geoq}

  # 没有经纬度信息，没有Boundary Collection不检查,需要做geoCoding
  unless (prop.lat and prop.lng and Boundary)
    return {isSkip:false}

  boundaryTags = await getBoundaryTagAsync {
    Boundary,
    prop: {
      lng: prop.lng,
      lat: prop.lat
    }
  }
  
  # 获取boundary city信息
  tagCity = boundaryTags?.bndCity?.nm?.toUpperCase() or ''
  propCity = prop.city.toUpperCase() # prop.city在当前函数之前已经校验,肯定存在
  
  # city与boundary city一致时认为loc信息准确
  if tagCity is propCity
    return {isSkip:true, geoQ:100, boundaryTags:boundaryTags}
  else if not Object.keys(boundaryTags).length
    # Boundary未找到相关信息,认为是偏远地区,跳过geoCoding
    return {isSkip:true, geoQ:90}
  else
    debug.warn "id:#{prop._id} #{prop.city} is not equal to #{tagCity}, lat:#{prop.lat}, lng: #{prop.lng}}"
    return {isSkip:false}

# 添加boundaryTag信息
addBoundaryInfo = (prop,boundaryTags = {})->
  for key in gTagFields
    if boundaryTags?[key]
      prop[key] = boundaryTags[key]

#resoSrc=OriginatingSystemName = [
#   'Brampton',
#   'Durham',
#   'Northumberland Hills',
#   'Oakville',
#   'Toronto',
#   'Toronto Regional Real Estate Board',
#   'itso',
#   'oreb',
#   'timmins'
# ]
# reso房源判断origSid,查找properties进行merge
mergeByOrigSid = (prop,PropertiesCol)->
  if (not prop.origSid) or (not prop.resoSrc)
    return
  if isDraftSid(prop.origSid)
    return
  if prop.resoSrc not in ['itso','oreb']
    return
  # new prop incoming is car prop
  isCar = false
  if prop.resoSrc is 'itso'
    origProp = await PropertiesCol.findOne {_id:"CAR#{prop.origSid}"}
    isCar = true
  else
    origProp = await PropertiesCol.findOne {_id:"OTW#{prop.origSid}"}
  if not origProp
    return
  
  # 因为oreb源已经停掉，所以不检查status,addr,daddr
  if isCar
    # 检查状态是否一致
    if origProp.status isnt prop.status
      debug.warn "id:#{prop._id} status:#{prop.status} is not equal to #{origProp._id} status:#{origProp.status}, skip merge"
      return
      
    # 检查地址信息
    # 1. 某个房源的addr不存在时,跳过merge
    if (not prop.addr) or (not origProp.addr)
      debug.warn "id:#{prop._id} or #{origProp._id} addr is missing, skip merge"
      return
    
    # 检查daddr的情况
    # 1. 两个房源都有daddr值
    if prop.daddr and origProp.daddr
      # 如果daddr不一致，则跳过merge
      if prop.daddr isnt origProp.daddr
        debug.warn "id:#{prop._id} daddr:#{prop.daddr} is not equal to #{origProp._id} daddr:#{origProp.daddr}, skip merge"
        return
    # 2. 当只有一个房源的daddr存在且为'N'时，跳过merge
    else if ((prop.daddr is 'N') and (not origProp.daddr)) or ((origProp.daddr is 'N') and (not prop.daddr))
      debug.warn "id:#{prop._id} or #{origProp._id} has daddr='N' while another is missing, skip merge"
      return
    # 3. 如果daddr都不存在时,可以进行merge, 此情况不需要特殊处理，继续执行后续代码即可
  
  prop.lat = origProp.lat
  prop.lng = origProp.lng
  # 添加aSIDs
  aSIDs = [
    {sid:prop.sid,id:prop._id},
    {sid:origProp.sid,id:origProp._id}
  ]
  aSIDs = pushOrigSidToASIDs prop,aSIDs
  aSIDs = pushOrigSidToASIDs origProp,aSIDs

  propInCol = await PropertiesCol.findOne {_id:prop._id}
  aSIDs = mergeAndRemoveDupASIDs(aSIDs,origProp.aSIDs)
  aSIDs = mergeAndRemoveDupASIDs(aSIDs,propInCol?.aSIDs)

  if isCar
    # CAR的房源reso优先级低,origProp添加bm,prop添加merged
    prop.merged = origProp._id
    await PropertiesCol.updateOne {_id:origProp._id},{$set:{aSIDs,bm:prop.bm}}
  else
    # aSIDs,merged 不能同时存在
    # OTW的房源reso优先级高,origProp应该被merge，incoming prop增加aSIDs
    prop.aSIDs = aSIDs
    await PropertiesCol.updateOne {_id:origProp._id},{$set:{merged:prop._id}}
  return prop

# record we care
isActiveOrSold = (prop={})->
  if prop.status is 'A'
    return true
  return /Lsd|Sld|Sc|Lc/.test prop.lst

###
trasform->format->check->geocoding->merge->save
* @param {string} srcType # source Type:'bcre|treb|ddf|property|trebMan'
* other params are collections
###
module.exports.convertAndSaveRecord = convertAndSaveRecord = (
  {
    srcType,
    noGeoForInactive,
    targetProperties, #maybe Properties or PropertiesNew
    record,
    TransitStop,
    transitCities,
    School,
    Boundary,
    Census2016,
    Census2016ReversKeys,
    Census2021,
    Census,
    TrebRecords,
    DDFRecords,
    BcreManualRecords,
    PropertiesImportLog,
    noTag,
    SqftMiddle,
    SqftsAggregate,
    SqftToProcess,
    AgentCol,
    OfficeCol,
    GeoCoder,
    ProvCityCmty, # 'prov_city_cmty',用于检查房源的city和prov是否匹配
    onlyConvert=false
  },
  cb
) ->
  sqftCols = {SqftMiddle,SqftsAggregate,SqftToProcess}
  debug.debug 'convertAndSaveRecord',record?._id
  unless record?._id
    debug.error 'no Record id',record,srcType
    return cb()
  convertfn =  getConvertParams srcType
  # debug.debug 'convertfn',convertfn
  if not convertfn
    return cb('no convert function found, error source type')
  # debug.info 'input record: ',record
  convertfn {
    srcRecord:record,
    TrebRecords,
    DDFRecords,
    BcreManualRecords,
    noTag,
    Boundary,
    AgentCol,
    OfficeCol,
    },(err,prop)->
      return cb(err) if err

      # Format propties.
      debug.debug 'prop before formatProp',prop._id
      metaInfo = {}
      prop = formatProp prop, metaInfo
      # 清理无效字符串 ptype2 = [] 被清理了
      # TODO: 函数已修复,需要整体测试之后再上线
      # prop = cleanInvalidFields prop
      # NOTE: formatProp会把origSid转为number
      prop.origSid = prop.origSid.toString() if prop.origSid?
      metaInfo.idBeforeMerge = prop._id # id before merge.
      metaInfo.lstlog = {
        Lsc: record.Lsc,
        lSrc:record.lSrc,
        lp: prop.lp or prop.lpr,
        ts: new Date(),
        step: 'import'
      }
      # debug.info 'format prop: ',prop.addr,prop.uaddr,prop.unt
      ## check prop value.
      try
        { prop, metaInfo } = await checkProp prop, metaInfo, targetProperties
      catch checkErr
        debug.error 'Error checking property', checkErr
        return cb(checkErr)
        
      debug.debug 'prop after checkProp',prop._id,metaInfo?.criticalError
      if metaInfo?.criticalError
        statImportHelper.updateStats {board:srcType,type:'error'}
        debug.info 'checkProp has critical error, skip import',\
          prop._id,metaInfo.criticalError

        try
          await saveMetaInfo {
            srcType,
            _id:prop._id,
            metaInfo,
            PropertiesImportLog}
        catch err
          return cb(err)
        return cb()
      # if incoming prop was merged by other props
      # with higher priorities,do not update anymore.
      # only update metaInfo.
      # targetProperties.findOne {_id:prop._id, merged:{$exists:true}},\
      # (err, ret)->
      #   return cb(err) if err
      #   debug.debug 'targetProperties merged',ret

      #   if ret or prop.metaInfo?.criticalError
      #     if ret
      #       debug.warn 'do not update merged prop',prop._id
      #       prop.metaInfo.wasMerged = 1
      #     else
      #       debug.warn 'checkProp has critical error, skip import',\
      #         prop._id,prop.metaInfo.criticalError

      #     return saveMetaInfo {
      #       srcType,
      #       _id:prop._id,
      #       metaInfo:prop.metaInfo,
      #       PropertiesImportLog},cb
      #   else
      if onlyConvert
        debug.debug 'onlyConvert'
        cb null, prop
        return

      # reso房源判断origSid,查找properties进行merge
      # 对历史的OTW/CAR的信息修改
      # OTW的房源 reso优先级高
      # CAR的房源 reso优先级低
      # TODO: 另外如果当前new prop能被mergeByOrigSid， 是否可以避免接下来的geoCode和merge?
      # NOTE: @fred 这里补充aSIDs和loc信息(此时会跳过geocoding),后面merge操作仍要进行
      try
        propInVOW = await targetProperties.findOne { _id: prop._id }
        hasQueriedVow = true
        await mergeByOrigSid prop,targetProperties
      catch err
        debug.error err
        return cb(err)

      # 统一检查房源loc信息,并添加keepPropGeocoding参数跳过geoCoding
      try
        await setPropGeoQAndBoundaryTags {prop,Boundary,GeoCoder,Properties:targetProperties,oldProp:propInVOW,hasQueriedVow}
      catch err
        debug.error err
        return cb(err)

      debug.info 'geoCoding+convertAndSaveRecord',prop._id or prop.id
      # noGeoForInactive param
      # console.log 'xxxxxx',prop._id,prop.status,prop.lst,noGeoForInactive,isActiveOrSold(prop)
      if noGeoForInactive and not isActiveOrSold(prop)
        prop.noGeocoding = true
      #update all properties even if it is merged
      doGeoCoding GeoCoder, prop, metaInfo, (err, item) ->
        if err
          debug.error err.toString()
        else
          # TODO: add stat and log which service;
          msg = "id:#{prop._id or prop.id}, q:#{item?.q or item?.msg}, cache:#{item?.cached}"
          debug.info "geoCoding done: #{msg}"
        
        # 检查房源的prov和city是否匹配
        try
          await checkProvAndCity {prop,ProvCityCmty,Boundary}
        catch err
          debug.error err
          return cb(err)

        saveToMongo {
          PropertiesImportLog
          Properties:targetProperties,
          prop,
          metaInfo,
          transitCities,
          TransitStop,
          School,
          Boundary,
          Census2016,
          Census2016ReversKeys,
          Census2021,
          Census,
          srcType,
          noTag,
          sqftCols,
          oldWithSameId: propInVOW,
          hasQueriedVow
        }, (err) ->
            speedObj = {}
            speedObj[srcType] = 1
            speedMeter.check speedObj
            gTotalProcessed[srcType]?=0
            if 0 is (gTotalProcessed[srcType]++ % 1000)
              debug.debug speedMeter
              relog speedMeter.toString()
              speedMeter.reset()
            if err # 数据库error，直接cb
              return cb(err)
            cb null, record, prop
