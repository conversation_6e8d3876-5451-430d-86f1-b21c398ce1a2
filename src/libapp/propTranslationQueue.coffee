debugHelper = require '../lib/debug'
debug = debugHelper.getDebugger()
ResourceDownloadQueue = require './mlsResourceDownloadQueue'

# 优先级阈值定义
HIGH_PRIORITY_THRESHOLD = 25000

class PropTranslationQueue extends ResourceDownloadQueue
  constructor: ->
    queueCol = COLLECTION('vow', 'prop_remark_trans_queue')
    super('PropTranslation', queueCol)
    @ensureIndexes()

  ensureIndexes: () ->
    try
      # 支持按状态和优先级查询的复合索引
      await @queueCol.createIndex { status: 1, priority: -1, _id: 1 }, { background: true }
      # 支持按创建时间查询的索引
      await @queueCol.createIndex { startTs: 1 }, { background: true }
      # 支持按重试次数查询的索引
      await @queueCol.createIndex { retry: 1 }, { background: true }
      # 支持清理查询的索引
      await @queueCol.createIndex { status: 1, endTs: 1 }, { background: true }
    catch error
      debug.error "PropTranslationQueue ensureIndexes error", error

  # 计算房源翻译优先级
  calculatePriority: (prop) ->
    priority = 0
    
    # 1. src 权重 (最高优先级)
    switch prop.src?.toUpperCase()
      when 'TRB' then priority += 20000
      when 'BRE' then priority += 18000
      when 'DDF', 'CAR', 'CLG', 'EDM' then priority += 16000
      else priority += 14000
    
    # 2. ptype 权重
    if prop.ptype is 'r'
      priority += 10000
    
    # 3. status 权重
    switch prop.status?.toUpperCase()
      when 'A' then priority += 5000
      when 'U' then priority += 3000
    
    # 4. saletp 权重
    switch prop.saletp?.toLowerCase()
      when 'sale' then priority += 2000
      when 'lease' then priority += 1000
    
    # 5. prov 权重
    if prop.prov?.toUpperCase() is 'ON'
      priority += 500
    
    return priority

  # 获取翻译服务列表（基于优先级）
  getTranslationServiceList: (priority, models = null) ->
    if models?.length > 0
      return models  # 使用指定的翻译服务
    
    if priority > HIGH_PRIORITY_THRESHOLD
      return ['gemini', 'rm', 'ovh']  # 高优先级优先使用gemini
    else
      return ['rm', 'ovh', 'gemini']  # 普通优先级优先使用rm/ovh

  # 添加翻译任务到队列
  addTranslationTask: (propId, prop, models = null) ->
    if not propId
      debug.error "PropTranslationQueue addTranslationTask: propId not found", propId
      return false
    
    if not prop?.m
      debug.info "PropTranslationQueue addTranslationTask: no content to translate", propId
      return false
    
    priority = @calculatePriority(prop)
    translationServices = @getTranslationServiceList(priority, models)
    
    doc = {
      _id: propId
      priority: priority
      models: translationServices
      status: 'pending'
      startTs: new Date()
      endTs: null
      retry: 0
      lastError: null
      content: prop.m  # 存储要翻译的内容
    }
    
    try
      await @queueCol.updateOne(
        {_id: propId},
        {$set: doc},
        {upsert: true}
      )
      debug.info "PropTranslationQueue addTranslationTask success", propId, priority
      return true
    catch error
      debug.error "PropTranslationQueue addTranslationTask error", error, propId
      return false

  # 获取下一批翻译任务
  getNextTranslationBatch: (batchSize = 50) ->
    try
      results = await @queueCol.findToArray(
        {status: 'pending'},
        {
          sort: {priority: -1, startTs: 1},
          limit: batchSize
        }
      )
      
      if results.length > 0
        # 更新任务状态为processing
        ids = results.map((record) -> record._id)
        await @queueCol.updateMany(
          {_id: {$in: ids}},
          {$set: {status: 'processing', startTs: new Date()}}
        )
        debug.info "PropTranslationQueue getNextTranslationBatch", results.length
        return results
      
      return []
    catch error
      debug.error "PropTranslationQueue getNextTranslationBatch error", error
      return []

  # 标记任务完成
  markTaskCompleted: (propId, translationResult, usedService) ->
    try
      await @queueCol.updateOne(
        {_id: propId},
        {
          $set: {
            status: 'completed'
            endTs: new Date()
            usedService: usedService
            lastError: null
          }
        }
      )
      debug.info "PropTranslationQueue markTaskCompleted", propId, usedService
    catch error
      debug.error "PropTranslationQueue markTaskCompleted error", error, propId

  # 标记任务失败并处理重试
  markTaskFailed: (propId, error, maxRetries = 3) ->
    try
      task = await @queueCol.findOne({_id: propId})
      if not task
        debug.error "PropTranslationQueue markTaskFailed: task not found", propId
        return
      
      retryCount = (task.retry or 0) + 1
      
      if retryCount >= maxRetries
        # 超过最大重试次数，标记为失败
        await @queueCol.updateOne(
          {_id: propId},
          {
            $set: {
              status: 'failed'
              endTs: new Date()
              retry: retryCount
              lastError: error?.message or error
            }
          }
        )
        debug.error "PropTranslationQueue markTaskFailed: max retries exceeded", propId, retryCount
      else
        # 重置为pending状态等待重试
        await @queueCol.updateOne(
          {_id: propId},
          {
            $set: {
              status: 'pending'
              retry: retryCount
              lastError: error?.message or error
            }
          }
        )
        debug.info "PropTranslationQueue markTaskFailed: retry", propId, retryCount
    catch dbError
      debug.error "PropTranslationQueue markTaskFailed error", dbError, propId

  # 获取队列统计信息
  getQueueStats: ->
    try
      stats = await @queueCol.aggregate([
        {
          $group: {
            _id: "$status"
            count: { $sum: 1 }
          }
        }
      ]).toArray()
      
      result = {
        pending: 0
        processing: 0
        completed: 0
        failed: 0
      }
      
      for stat in stats
        result[stat._id] = stat.count
      
      return result
    catch error
      debug.error "PropTranslationQueue getQueueStats error", error
      return null

  # 清理已完成的旧任务
  cleanupCompletedTasks: (daysOld = 7) ->
    try
      cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000)
      result = await @queueCol.deleteMany({
        status: 'completed'
        endTs: { $lt: cutoffDate }
      })
      debug.info "PropTranslationQueue cleanupCompletedTasks", result.deletedCount
      return result.deletedCount
    catch error
      debug.error "PropTranslationQueue cleanupCompletedTasks error", error
      return 0

module.exports = PropTranslationQueue
