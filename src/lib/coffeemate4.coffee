###
NOTE: this file is for unittest

coffeemate4 has two roles:
1. compile coffee and ts to js
2. load apps & start server
###

mate    = require './appBase'
debug   = require('./debug').getDebugger(1)
helpers = require './helpers'
loader  = require('./loader').loader
path    = require 'path'
fs      = require 'fs'
srcDir  = path.dirname(__dirname)
builtDir = path.join(srcDir,'built')

headerKeywordsReg = null
buildHeader = (code,subDirs,isBatch)->
  if keys = code.match headerKeywordsReg
    # debug.info keys
    keys = Object.keys keys.reduce((
      (obj,key)->
        obj[key] = 1
        return obj),{})
    return "const {#{keys.join(',')}}=require('" + \
      '../'.repeat(subDirs.length) + 'lib/' + \
      (if isBatch then 'batchBase' else 'appBase') + \
      "').coffeemate.locals;"
  null

# writeTo = []
# compile a single file
compileSingleFile = (fullpath,file,curSubDir,isBatch,isLib)->
  debug.verbose 'compile',file
  jsCodeObj = loader.readAndCompile fullpath,{
    header: null,
    throwError:true,
    coffeeOpt:{sourceMap:true,bare:false}
  }
  # add suitable header code
  if not isLib
    if headerCode = buildHeader jsCodeObj.coffeeCode,curSubDir,isBatch
      jsCodeObj.js = headerCode + jsCodeObj.js
  # generate target file
  filejs = file.replace /\.coffee$/,'.js'
  targetPath = path.join(builtDir,curSubDir.join('/'),filejs)
  debug.verbose 'write to',targetPath
  # writeTo.push targetPath
  # if (writeTo.length %20) is 0
  #   debug.vererbose 'write to',writeTo
  #   writeTo = []
  unless jsCodeObj
    debug.error targetPath,'Compile Error'
    return
  try
    fs.writeFileSync targetPath,jsCodeObj.js
  catch e
    console.error e
    console.error jsCodeObj

# srcDir is relative to srcDir, like 'lib'
compileDir = (srcBase)->
  debug.info 'compile folder',srcBase
  isBatch = srcBase is 'batch'
  isLib = srcBase is 'lib'
  curSubDir = [srcBase]
  compileFile = (fullpath,file)->
    compileSingleFile fullpath,file,curSubDir,isBatch,isLib
  # make sure the targe folder exist
  fs.mkdirSync (path.join builtDir,curSubDir.join('/')), { recursive: true }
    # map file
    # if jsCodeObj.SourceMap or jsCodeObj.v3SourceMap
    #   fs.writeFileSync (targetPath + '.map'),(jsCodeObj.SourceMap or jsCodeObj.v3SourceMap)
  shallSkipFile = (file)->
    if ret = /^(\~|\.)|unused/.test(file)
      debug.verbose 'skipFile',file
    else if ret = not /\.coffee$/.test(file)
      if /\.(js|ts|dot|html|htm|css|scss)$/.test file
        fullpath = path.join(srcDir,curSubDir.join('/'),file)
        debug.verbose 'copyFile',file
        fs.copyFileSync fullpath,\
          path.join(builtDir,curSubDir.join('/'),file)
      else
        if not /\.(pbf|csv|md|py|zip|json|txt)$/.test(file)
          debug.warn 'skipFile',file
    ret
  shallSkipDir = (file)->
    if ret = /^(\~|\.)|unused/.test(file)
      debug.info 'skipDir',file
    else # enter into a deeper level dir
      curSubDir.push file
      fs.mkdirSync (path.join builtDir,curSubDir.join('/')), { recursive: true }
      debug.verbose 'change dir+',curSubDir
    ret
  endDir = -> # jump up a dir level
    curSubDir.pop()
    debug.verbose 'change dir-',curSubDir
  helpers.walkDirSync {
    dir:path.join(srcDir,srcBase),
    callback:compileFile,
    shallSkipFile,
    shallSkipDir,
    endDir}

compileFiles = (cfgfile,toCompileDirs)->
  options = mate.readConfig cfgfile
  keywords = mate.coffeemate.localNames.concat mate.coffeemate.localNameExtras
  keywords = (n.toUpperCase() for n in keywords)
  headerKeywordsReg = new RegExp('\\b(' + keywords.join('|') + ')\\b','g')
  debug.info 'headerKeywords',keywords
  # compile lib,libapp recursively
  for dir in toCompileDirs
    if (filePath = dir.split('/')).length > 1
      fullpath = path.join(srcDir,dir)
      file = filePath[filePath.length - 1]
      curSubDir = filePath.slice(0,filePath.length - 1)
      isBatch = filePath[0] is 'batch'
      isLib = filePath[0] is 'lib'
      compileSingleFile fullpath,file,curSubDir,isBatch,isLib
    else
      compileDir dir

exports.runService = runService = (cfgfile,cb)->
  # start up service
  mate.runConfig(cfgfile,cb)

# filter for results 
getOutliers = (obj)->
  ret = {}
  for k,ms of obj
    if ms > 100
      ret[k] = ms
  ret

debug.info 'runing coffeemate4'
if require.main is module
  # if process.argv.length > 2 and process.argv[0] is 'coffee'
  #   cfgfile = process.argv[2]
  # else
  if process.argv.length > 0
    for argv in process.argv
      # config4.coffee or unit_test.ini
      if /config\d*\.coffee$|unit_test\.ini$|unit_testES\.ini$/.test(argv)
        cfgfile = argv
  compileOnly = false
  toCompileDirs = []
  for argv in process.argv
    if /^compile$/i.test argv
      compileOnly = true
    else if compileOnly
      toCompileDirs.push argv
  debug.info 'cfgfile:',cfgfile,' compileOnly:',compileOnly
  if cfgfile?
    if compileOnly
      if toCompileDirs.length is 0
        # TODO: should read config.apps
        toCompileDirs = ['lib', 'libapp', 'apps','apps_common',\
                      'model','batch','migrate','appImgServer','microService']
        if fs.existsSync(builtDir)
          fs.rmdirSync builtDir, { recursive: true }
      fs.mkdirSync builtDir, { recursive: true }
      compileFiles cfgfile, toCompileDirs
      debug.info 'loadtimes: ',getOutliers(loader.loadTimes())
      process.exit 0
    else
      runService cfgfile,->
        debug.info 'coffeemate service started'
  else
    debug.error 'Please specify configuration file!'
    process.exit(1)