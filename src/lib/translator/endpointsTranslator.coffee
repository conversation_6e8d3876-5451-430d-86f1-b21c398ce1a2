debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AITranslator = require './AITranslator'

class EndpointsTranslator extends AITranslator
  constructor: (endpoint, model, prompt, maxUsage) ->
    super(null, endpoint, model, prompt, maxUsage)
    
  translate: (message, fromLang="English", toLang="Chinese") ->
    data = {
      model: @model
      messages: [
        { role: "user", content: "#{@prompt} #{fromLang} to #{toLang}: #{message}" }
      ]
      stream: false
    }
    
    try
      @use()
      response = await fetch(@endpoint,
        method: 'POST',
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(data)
        signal: AbortSignal.timeout(30000)  # 30 seconds timeout
      )
      
      debug.debug 'EndpointsTranslator response status:', response.status
      
      if not response.ok
        throw new Error("HTTP error! status: #{response.status}")

      ret = await response.json()
      debug.debug 'EndpointsTranslator response:', ret
      
      if ret.choices?[0]?.message?.content
        translatedContent = ret.choices[0].message.content
        # 清理换行符
        translatedContentCleaned = translatedContent.replace(/\n/g, "")
        return translatedContentCleaned
      else if ret.response?
        # 兼容其他格式的响应
        translatedContent = ret.response
        translatedContentCleaned = translatedContent.replace(/\n/g, "")
        return translatedContentCleaned
      else
        throw new Error("Empty or invalid response from Endpoints translation API")
        
    catch error
      debug.error "Endpoints Translation API Error:", {
        error: {
          name: error.name
          message: error.message
          stack: error.stack
          details: error
        }
        response: ret || 'No response parsed'
      }
      throw error
    finally
      @release()

module.exports = EndpointsTranslator
