debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AzureTranslator = require './azureTranslator'
DeepLTranslator = require './deepLTranslator'
DeepSeekTranslator = require './deepseekTranslator'
OpenAITranslator = require './openAITranslator'
GeminiTranslator = require './geminiTranslator'
ClaudeTranslator = require './claudeTranslator'
GrokTranslator = require './grokTranslator'
RMTranslator = require './rmTranslator'
EndpointsTranslator = require './endpointsTranslator'

PROMPT="You are a real estate expert. The following content is about Canada real estate listing. Please translate all of the following content, but do not translate place names. Only return the translated text, do not include any other text. Translate the following text from English to Chinese."
MODEL_DEEPSEEK="deepseek-chat"
MODEL_OPENAI="gpt-4o-mini"
MODEL_GEMINI="gemini-2.5-flash-lite" #"gemini-1.5-flash"
MODEL_CLAUDE="claude-3-haiku-20240307"
MODEL_GROK="grok-2-latest"
MODEL_RM="gemma3:12b"
TRANSLATORLIST = ['gemini', 'openAI', 'claude', 'azure'] # grok can not be used in app now because of the request limit

LANGUAGE_OBJ = [
  {k: 'en', v:'English', AIValue: 'English', azureValue: 'en', deepLValue:'EN'},
  {k: 'zh-cn', v:'简体中文', AIValue: 'Chinese', azureValue: 'zh-Hans', deepLValue:'ZH'},
  {k: 'zh', v:'繁体中文', AIValue: 'Traditional Chinese', azureValue: 'zh-Hant', deepLValue:'ZH-HANT'},
  {k: 'kr', v:'한국어', AIValue: 'Korean', azureValue: 'ko', deepLValue:'KO'},
]

findLanguageValue = (k, service) ->
  result = LANGUAGE_OBJ.find (language) -> language.k is k
  if result?
    switch service
      when 'azure' then result.azureValue
      when 'deepl' then result.deepLValue
      when 'deepseek', 'openAI', 'gemini', 'claude', 'grok', 'rm' then result.AIValue
      else throw new Error "Service '#{service}' not supported"
  else
    throw new Error "Key '#{k}' not found in LANGUAGE_OBJ"

class TranslatorManager
  constructor: (config) ->
    debug.debug 'TranslatorManager config:', config
    @translators = {}
    @waitingQueue = []
    
    # Initialize translators with their max usage limits from config
    if config?.azure?.subscriptionKey
      @translators['azure'] = new AzureTranslator(config.azure.subscriptionKey, config.azure.endpoint, config.azure.region, config.azure.maxUsage)
      
    if config?.deepL?.key
      @translators['deepl'] = new DeepLTranslator(config.deepL.key, config.deepL.endpoint, config.deepL.maxUsage)
      
    if config?.deepseek?.key
      @translators['deepseek'] = new DeepSeekTranslator(config.deepseek.key, config.deepseek.endpoint, MODEL_DEEPSEEK, PROMPT, config.deepseek.maxUsage)
      
    if config?.openAI?.key
      @translators['openAI'] = new OpenAITranslator(config.openAI.key, config.openAI.endpoint, MODEL_OPENAI, PROMPT, config.openAI.orgID, config.openAI.projectID, config.openAI.maxUsage)
      
    if config?.gemini?.key
      @translators['gemini'] = new GeminiTranslator(config.gemini.key, MODEL_GEMINI, PROMPT, config.gemini.maxUsage)
      
    if config?.claude?.key
      @translators['claude'] = new ClaudeTranslator(config.claude.key, config.claude.endpoint, MODEL_CLAUDE, PROMPT, config.claude.maxUsage)
      
    if config?.grok?.key
      debug.debug 'Initializing Grok translator with key:', config.grok.key
      @translators['grok'] = new GrokTranslator(config.grok.key, config.grok.endpoint, MODEL_GROK, PROMPT, config.grok.maxUsage)
      
    if config?.rm?.endpoint
      @translators['rm'] = new RMTranslator(config.rm.endpoint, MODEL_RM, PROMPT, config.rm.maxUsage)

    if config?.endpoints?.endpoint
      @translators['endpoints'] = new EndpointsTranslator(config.endpoints.endpoint, config.endpoints.model or 'endpoints-model', PROMPT, config.endpoints.maxUsage)

  getAvailableTranslator: (translatorList) ->
    debug.debug '###getAvailableTranslator',translatorList
    # Check translators in the order of translatorList
    for service in translatorList
      debug.debug '###service',service,@translators[service].usageCount, @translators[service].maxUsage,  @translators[service].isAvailable()
      if @translators[service]?.isAvailable()
        return service
    return null

  processWaitingQueue: ->
    debug.debug '###processWaitingQueue',@waitingQueue.length
    if @waitingQueue.length > 0
      # Get the first waiting resolve function and execute it
      resolve = @waitingQueue.shift()
      resolve()

  translate: (message, translatorList=TRANSLATORLIST, fromLangKey='en', toLangKey='zh-cn') ->
    attemptTranslation = (index) =>
      if index >= translatorList.length
        throw new Error "Translation failed with all services"

      service = translatorList[index]
      translator = @translators[service]

      if translator?
        try
          fromLang = findLanguageValue(fromLangKey, service)
          toLang = findLanguageValue(toLangKey, service)
          
          # Check if translator is available
          if not translator.isAvailable()
            debug.debug "Translator #{service} is at max usage, trying next service"
            return attemptTranslation(index + 1)
            
          debug.debug "Using translator #{service}"
          
          result = await translator.translate(message, fromLang, toLang)
          
          # Process waiting queue after translation
          @processWaitingQueue()
          
          if result and result isnt ''
            return [result, service]
            
          debug.warn "Empty translation result from #{service}, trying next service"
          return attemptTranslation(index + 1)
        catch error
          # Process waiting queue on error
          @processWaitingQueue()
          
          if error.name is 'authentication_error'
            debug.error "#{service} Translation API Authentication Error"
            throw error
          else
            debug.error "Error using #{service} translator:", {
              errorName: error.name
              errorMessage: error.message
              errorDetails: error
              service: service
            }
            return attemptTranslation(index + 1)
      else
        debug.error "Translator service not found: #{service}"
        return attemptTranslation(index + 1)

    # Try to get an available translator
    service = @getAvailableTranslator(translatorList)
    if not service?
      # If no translator is available, add to waiting queue
      debug.debug "No translator available, adding to waiting queue"
      await new Promise (resolve) =>
        @waitingQueue.push resolve
      # After being woken up, try again
      service = @getAvailableTranslator(translatorList)
      if not service?
        throw new Error "No translator available after waiting"

    # Start translation with the available translator
    return await attemptTranslation(translatorList.indexOf(service))

exports.createTranslatorManager = (config)-> new TranslatorManager(config)
exports.findLanguageValue = findLanguageValue
