# 需求 [addqueue_translate_remark]

## 反馈

1. Rain反馈房源prop.m的翻译请求改用queue，高优先级的可以用gemini，否则用我们自己的rm翻译或者ovh
2. Fred反馈prop.m 根据是用户点击(房源详情中的翻译按钮,和刘蕊确认一下,需要实时翻译)还是自然import进来，可以使用EP的类型区分一下。还是要有pool size的限制。rm/ovh是都可以用，gemini是需要立刻处理的，而前面都用没了，用这个。

## 需求提出人:    Rain/Fred

## 修改人：       Luo xiaowei

## 提出日期:      2025-08-23

## 解决办法

1. 添加`PropTranslationQueue`类(可以继承`ResourceDownloadQueue`类),`prop_remark_trans_queue`表
  **propTranslationQueue集合字段说明**：
  - **_id**: 房源ID，直接使用房源ID作为队列任务ID
  - **priority**: 优先级数值
  - **models**: 可用翻译服务列表，如['gemini']或['rm', 'ovh'], 缺省 可以为空
  - **status**: 任务状态，包括'pending'（待处理）、'processing'（处理中）、'completed'（已完成）、'failed'（失败）
  - **startTs**: 开始处理时间戳
  - **endTs**: 处理结束时间戳
  - **retry**: 重试次数计数器
  - **lastError**: 最后一次错误信息记录
2. 房源导入/更新`m`时,添加记录到queue。(src/libapp/saveToMaster.coffee文件`updateOldProp`函数reset `m_zh`时,以及`createNewProp`函数中添加记录)
  **优先级计算逻辑**：
  权重顺序: src > ptype > status > saletp > prov
  **1. src**
  - TRB: 20000
  - BRE: 18000
  - DDF: 16000
  - CAR: 16000
  - CLG: 16000
  - EDM: 16000
  - 其他: 14000
  **2. ptype**
  - r (residential): 10000
  **3. status**
  - A (Active): 5000
  - U (Inactive): 3000
  **4. saletp**
  - Sale: 2000
  - Lease: 1000
  **5. prov**
  - ON (Ontario): 500
3. 新增batch专门处理翻译队列,代替现有的`watchPropAndTranslate.coffee`文件
  1. 初始化和配置 -> 加载AI翻译配置, 设置处理参数。
  2. 任务获取逻辑 -> 按优先级降序和创建时间升序排序获取待处理任务
  3. 翻译与更新数据 -> 根据任务优先级和服务可用性选择最佳翻译服务, 执行翻译后更新房源的`m_zh`字段, 记录处理结果和使用的翻译服务
  4. 错误处理和重试 -> 翻译失败时根据重试次数决定是否重试, 超过最大重试次数的任务标记为失败状态, 记录错误信息
  5. 主处理循环 -> 定期从队列获取任务进行批量处理
4. 添加 Endpoints AI翻译模型
5. AI翻译模型添加pool size支持, 现在的`maxUsage`还未使用, 需要添加使用

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025--28

## online-step
